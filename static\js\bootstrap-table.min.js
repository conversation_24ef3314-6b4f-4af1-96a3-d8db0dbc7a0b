/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.19.1
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):(t="undefined"!=typeof globalThis?globalThis:t||self).BootstrapTable=e(t.jQuery)}(this,(function(t){"use strict";function e(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var i=e(t);function n(t){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function o(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function a(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function s(t,e,i){return e&&a(t.prototype,e),i&&a(t,i),t}function r(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(t)))return;var i=[],n=!0,o=!1,a=void 0;try{for(var s,r=t[Symbol.iterator]();!(n=(s=r.next()).done)&&(i.push(s.value),!e||i.length!==e);n=!0);}catch(t){o=!0,a=t}finally{try{n||null==r.return||r.return()}finally{if(o)throw a}}return i}(t,e)||c(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(t){return function(t){if(Array.isArray(t))return h(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||c(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(t,e){if(t){if("string"==typeof t)return h(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?h(t,e):void 0}}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}function u(t,e){var i;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(i=c(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,r=!1;return{s:function(){i=t[Symbol.iterator]()},n:function(){var t=i.next();return s=t.done,t},e:function(t){r=!0,a=t},f:function(){try{s||null==i.return||i.return()}finally{if(r)throw a}}}}var d="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function f(t,e){return t(e={exports:{}},e.exports),e.exports}var p=function(t){return t&&t.Math==Math&&t},g=p("object"==typeof globalThis&&globalThis)||p("object"==typeof window&&window)||p("object"==typeof self&&self)||p("object"==typeof d&&d)||function(){return this}()||Function("return this")(),v=function(t){try{return!!t()}catch(t){return!0}},b=!v((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),m={}.propertyIsEnumerable,y=Object.getOwnPropertyDescriptor,w={f:y&&!m.call({1:2},1)?function(t){var e=y(this,t);return!!e&&e.enumerable}:m},S=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},x={}.toString,k=function(t){return x.call(t).slice(8,-1)},O="".split,C=v((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==k(t)?O.call(t,""):Object(t)}:Object,T=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t},P=function(t){return C(T(t))},I=function(t){return"object"==typeof t?null!==t:"function"==typeof t},A=function(t,e){if(!I(t))return t;var i,n;if(e&&"function"==typeof(i=t.toString)&&!I(n=i.call(t)))return n;if("function"==typeof(i=t.valueOf)&&!I(n=i.call(t)))return n;if(!e&&"function"==typeof(i=t.toString)&&!I(n=i.call(t)))return n;throw TypeError("Can't convert object to primitive value")},$={}.hasOwnProperty,E=function(t,e){return $.call(t,e)},R=g.document,j=I(R)&&I(R.createElement),_=function(t){return j?R.createElement(t):{}},N=!b&&!v((function(){return 7!=Object.defineProperty(_("div"),"a",{get:function(){return 7}}).a})),F=Object.getOwnPropertyDescriptor,D={f:b?F:function(t,e){if(t=P(t),e=A(e,!0),N)try{return F(t,e)}catch(t){}if(E(t,e))return S(!w.f.call(t,e),t[e])}},V=function(t){if(!I(t))throw TypeError(String(t)+" is not an object");return t},B=Object.defineProperty,L={f:b?B:function(t,e,i){if(V(t),e=A(e,!0),V(i),N)try{return B(t,e,i)}catch(t){}if("get"in i||"set"in i)throw TypeError("Accessors not supported");return"value"in i&&(t[e]=i.value),t}},H=b?function(t,e,i){return L.f(t,e,S(1,i))}:function(t,e,i){return t[e]=i,t},M=function(t,e){try{H(g,t,e)}catch(i){g[t]=e}return e},U="__core-js_shared__",z=g[U]||M(U,{}),q=Function.toString;"function"!=typeof z.inspectSource&&(z.inspectSource=function(t){return q.call(t)});var W,G,K,Y=z.inspectSource,X=g.WeakMap,J="function"==typeof X&&/native code/.test(Y(X)),Q=f((function(t){(t.exports=function(t,e){return z[t]||(z[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.10.1",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),Z=0,tt=Math.random(),et=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++Z+tt).toString(36)},it=Q("keys"),nt=function(t){return it[t]||(it[t]=et(t))},ot={},at=g.WeakMap;if(J){var st=z.state||(z.state=new at),rt=st.get,lt=st.has,ct=st.set;W=function(t,e){return e.facade=t,ct.call(st,t,e),e},G=function(t){return rt.call(st,t)||{}},K=function(t){return lt.call(st,t)}}else{var ht=nt("state");ot[ht]=!0,W=function(t,e){return e.facade=t,H(t,ht,e),e},G=function(t){return E(t,ht)?t[ht]:{}},K=function(t){return E(t,ht)}}var ut={set:W,get:G,has:K,enforce:function(t){return K(t)?G(t):W(t,{})},getterFor:function(t){return function(e){var i;if(!I(e)||(i=G(e)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return i}}},dt=f((function(t){var e=ut.get,i=ut.enforce,n=String(String).split("String");(t.exports=function(t,e,o,a){var s,r=!!a&&!!a.unsafe,l=!!a&&!!a.enumerable,c=!!a&&!!a.noTargetGet;"function"==typeof o&&("string"!=typeof e||E(o,"name")||H(o,"name",e),(s=i(o)).source||(s.source=n.join("string"==typeof e?e:""))),t!==g?(r?!c&&t[e]&&(l=!0):delete t[e],l?t[e]=o:H(t,e,o)):l?t[e]=o:M(e,o)})(Function.prototype,"toString",(function(){return"function"==typeof this&&e(this).source||Y(this)}))})),ft=g,pt=function(t){return"function"==typeof t?t:void 0},gt=function(t,e){return arguments.length<2?pt(ft[t])||pt(g[t]):ft[t]&&ft[t][e]||g[t]&&g[t][e]},vt=Math.ceil,bt=Math.floor,mt=function(t){return isNaN(t=+t)?0:(t>0?bt:vt)(t)},yt=Math.min,wt=function(t){return t>0?yt(mt(t),9007199254740991):0},St=Math.max,xt=Math.min,kt=function(t,e){var i=mt(t);return i<0?St(i+e,0):xt(i,e)},Ot=function(t){return function(e,i,n){var o,a=P(e),s=wt(a.length),r=kt(n,s);if(t&&i!=i){for(;s>r;)if((o=a[r++])!=o)return!0}else for(;s>r;r++)if((t||r in a)&&a[r]===i)return t||r||0;return!t&&-1}},Ct={includes:Ot(!0),indexOf:Ot(!1)},Tt=Ct.indexOf,Pt=function(t,e){var i,n=P(t),o=0,a=[];for(i in n)!E(ot,i)&&E(n,i)&&a.push(i);for(;e.length>o;)E(n,i=e[o++])&&(~Tt(a,i)||a.push(i));return a},It=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],At=It.concat("length","prototype"),$t={f:Object.getOwnPropertyNames||function(t){return Pt(t,At)}},Et={f:Object.getOwnPropertySymbols},Rt=gt("Reflect","ownKeys")||function(t){var e=$t.f(V(t)),i=Et.f;return i?e.concat(i(t)):e},jt=function(t,e){for(var i=Rt(e),n=L.f,o=D.f,a=0;a<i.length;a++){var s=i[a];E(t,s)||n(t,s,o(e,s))}},_t=/#|\.prototype\./,Nt=function(t,e){var i=Dt[Ft(t)];return i==Bt||i!=Vt&&("function"==typeof e?v(e):!!e)},Ft=Nt.normalize=function(t){return String(t).replace(_t,".").toLowerCase()},Dt=Nt.data={},Vt=Nt.NATIVE="N",Bt=Nt.POLYFILL="P",Lt=Nt,Ht=D.f,Mt=function(t,e){var i,n,o,a,s,r=t.target,l=t.global,c=t.stat;if(i=l?g:c?g[r]||M(r,{}):(g[r]||{}).prototype)for(n in e){if(a=e[n],o=t.noTargetGet?(s=Ht(i,n))&&s.value:i[n],!Lt(l?n:r+(c?".":"#")+n,t.forced)&&void 0!==o){if(typeof a==typeof o)continue;jt(a,o)}(t.sham||o&&o.sham)&&H(a,"sham",!0),dt(i,n,a,t)}},Ut="\t\n\v\f\r                　\u2028\u2029\ufeff",zt="["+Ut+"]",qt=RegExp("^"+zt+zt+"*"),Wt=RegExp(zt+zt+"*$"),Gt=function(t){return function(e){var i=String(T(e));return 1&t&&(i=i.replace(qt,"")),2&t&&(i=i.replace(Wt,"")),i}},Kt={start:Gt(1),end:Gt(2),trim:Gt(3)},Yt=Kt.trim;Mt({target:"String",proto:!0,forced:function(t){return v((function(){return!!Ut[t]()||"​᠎"!="​᠎"[t]()||Ut[t].name!==t}))}("trim")},{trim:function(){return Yt(this)}});var Xt=function(t,e){var i=[][t];return!!i&&v((function(){i.call(null,e||function(){throw 1},1)}))},Jt=[].join,Qt=C!=Object,Zt=Xt("join",",");Mt({target:"Array",proto:!0,forced:Qt||!Zt},{join:function(t){return Jt.call(P(this),void 0===t?",":t)}});var te=function(){var t=V(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e};function ee(t,e){return RegExp(t,e)}var ie={UNSUPPORTED_Y:v((function(){var t=ee("a","y");return t.lastIndex=2,null!=t.exec("abcd")})),BROKEN_CARET:v((function(){var t=ee("^r","gy");return t.lastIndex=2,null!=t.exec("str")}))},ne=RegExp.prototype.exec,oe=Q("native-string-replace",String.prototype.replace),ae=ne,se=function(){var t=/a/,e=/b*/g;return ne.call(t,"a"),ne.call(e,"a"),0!==t.lastIndex||0!==e.lastIndex}(),re=ie.UNSUPPORTED_Y||ie.BROKEN_CARET,le=void 0!==/()??/.exec("")[1];(se||le||re)&&(ae=function(t){var e,i,n,o,a=this,s=re&&a.sticky,r=te.call(a),l=a.source,c=0,h=t;return s&&(-1===(r=r.replace("y","")).indexOf("g")&&(r+="g"),h=String(t).slice(a.lastIndex),a.lastIndex>0&&(!a.multiline||a.multiline&&"\n"!==t[a.lastIndex-1])&&(l="(?: "+l+")",h=" "+h,c++),i=new RegExp("^(?:"+l+")",r)),le&&(i=new RegExp("^"+l+"$(?!\\s)",r)),se&&(e=a.lastIndex),n=ne.call(s?i:a,h),s?n?(n.input=n.input.slice(c),n[0]=n[0].slice(c),n.index=a.lastIndex,a.lastIndex+=n[0].length):a.lastIndex=0:se&&n&&(a.lastIndex=a.global?n.index+n[0].length:e),le&&n&&n.length>1&&oe.call(n[0],i,(function(){for(o=1;o<arguments.length-2;o++)void 0===arguments[o]&&(n[o]=void 0)})),n});var ce=ae;Mt({target:"RegExp",proto:!0,forced:/./.exec!==ce},{exec:ce});var he,ue,de="process"==k(g.process),fe=gt("navigator","userAgent")||"",pe=g.process,ge=pe&&pe.versions,ve=ge&&ge.v8;ve?ue=(he=ve.split("."))[0]+he[1]:fe&&(!(he=fe.match(/Edge\/(\d+)/))||he[1]>=74)&&(he=fe.match(/Chrome\/(\d+)/))&&(ue=he[1]);var be=ue&&+ue,me=!!Object.getOwnPropertySymbols&&!v((function(){return!Symbol.sham&&(de?38===be:be>37&&be<41)})),ye=me&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,we=Q("wks"),Se=g.Symbol,xe=ye?Se:Se&&Se.withoutSetter||et,ke=function(t){return E(we,t)&&(me||"string"==typeof we[t])||(me&&E(Se,t)?we[t]=Se[t]:we[t]=xe("Symbol."+t)),we[t]},Oe=ke("species"),Ce=!v((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")})),Te="$0"==="a".replace(/./,"$0"),Pe=ke("replace"),Ie=!!/./[Pe]&&""===/./[Pe]("a","$0"),Ae=!v((function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var i="ab".split(t);return 2!==i.length||"a"!==i[0]||"b"!==i[1]})),$e=function(t,e,i,n){var o=ke(t),a=!v((function(){var e={};return e[o]=function(){return 7},7!=""[t](e)})),s=a&&!v((function(){var e=!1,i=/a/;return"split"===t&&((i={}).constructor={},i.constructor[Oe]=function(){return i},i.flags="",i[o]=/./[o]),i.exec=function(){return e=!0,null},i[o](""),!e}));if(!a||!s||"replace"===t&&(!Ce||!Te||Ie)||"split"===t&&!Ae){var r=/./[o],l=i(o,""[t],(function(t,e,i,n,o){return e.exec===RegExp.prototype.exec?a&&!o?{done:!0,value:r.call(e,i,n)}:{done:!0,value:t.call(i,e,n)}:{done:!1}}),{REPLACE_KEEPS_$0:Te,REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE:Ie}),c=l[0],h=l[1];dt(String.prototype,t,c),dt(RegExp.prototype,o,2==e?function(t,e){return h.call(t,this,e)}:function(t){return h.call(t,this)})}n&&H(RegExp.prototype[o],"sham",!0)},Ee=ke("match"),Re=function(t){var e;return I(t)&&(void 0!==(e=t[Ee])?!!e:"RegExp"==k(t))},je=function(t){if("function"!=typeof t)throw TypeError(String(t)+" is not a function");return t},_e=ke("species"),Ne=function(t){return function(e,i){var n,o,a=String(T(e)),s=mt(i),r=a.length;return s<0||s>=r?t?"":void 0:(n=a.charCodeAt(s))<55296||n>56319||s+1===r||(o=a.charCodeAt(s+1))<56320||o>57343?t?a.charAt(s):n:t?a.slice(s,s+2):o-56320+(n-55296<<10)+65536}},Fe={codeAt:Ne(!1),charAt:Ne(!0)}.charAt,De=function(t,e,i){return e+(i?Fe(t,e).length:1)},Ve=function(t,e){var i=t.exec;if("function"==typeof i){var n=i.call(t,e);if("object"!=typeof n)throw TypeError("RegExp exec method returned something other than an Object or null");return n}if("RegExp"!==k(t))throw TypeError("RegExp#exec called on incompatible receiver");return ce.call(t,e)},Be=ie.UNSUPPORTED_Y,Le=[].push,He=Math.min,Me=4294967295;$e("split",2,(function(t,e,i){var n;return n="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(t,i){var n=String(T(this)),o=void 0===i?Me:i>>>0;if(0===o)return[];if(void 0===t)return[n];if(!Re(t))return e.call(n,t,o);for(var a,s,r,l=[],c=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),h=0,u=new RegExp(t.source,c+"g");(a=ce.call(u,n))&&!((s=u.lastIndex)>h&&(l.push(n.slice(h,a.index)),a.length>1&&a.index<n.length&&Le.apply(l,a.slice(1)),r=a[0].length,h=s,l.length>=o));)u.lastIndex===a.index&&u.lastIndex++;return h===n.length?!r&&u.test("")||l.push(""):l.push(n.slice(h)),l.length>o?l.slice(0,o):l}:"0".split(void 0,0).length?function(t,i){return void 0===t&&0===i?[]:e.call(this,t,i)}:e,[function(e,i){var o=T(this),a=null==e?void 0:e[t];return void 0!==a?a.call(e,o,i):n.call(String(o),e,i)},function(t,o){var a=i(n,t,this,o,n!==e);if(a.done)return a.value;var s=V(t),r=String(this),l=function(t,e){var i,n=V(t).constructor;return void 0===n||null==(i=V(n)[_e])?e:je(i)}(s,RegExp),c=s.unicode,h=(s.ignoreCase?"i":"")+(s.multiline?"m":"")+(s.unicode?"u":"")+(Be?"g":"y"),u=new l(Be?"^(?:"+s.source+")":s,h),d=void 0===o?Me:o>>>0;if(0===d)return[];if(0===r.length)return null===Ve(u,r)?[r]:[];for(var f=0,p=0,g=[];p<r.length;){u.lastIndex=Be?0:p;var v,b=Ve(u,Be?r.slice(p):r);if(null===b||(v=He(wt(u.lastIndex+(Be?p:0)),r.length))===f)p=De(r,p,c);else{if(g.push(r.slice(f,p)),g.length===d)return g;for(var m=1;m<=b.length-1;m++)if(g.push(b[m]),g.length===d)return g;p=f=v}}return g.push(r.slice(f)),g}]}),Be);var Ue=Object.keys||function(t){return Pt(t,It)},ze=w.f,qe=function(t){return function(e){for(var i,n=P(e),o=Ue(n),a=o.length,s=0,r=[];a>s;)i=o[s++],b&&!ze.call(n,i)||r.push(t?[i,n[i]]:n[i]);return r}},We={entries:qe(!0),values:qe(!1)}.entries;Mt({target:"Object",stat:!0},{entries:function(t){return We(t)}});var Ge,Ke=b?Object.defineProperties:function(t,e){V(t);for(var i,n=Ue(e),o=n.length,a=0;o>a;)L.f(t,i=n[a++],e[i]);return t},Ye=gt("document","documentElement"),Xe=nt("IE_PROTO"),Je=function(){},Qe=function(t){return"<script>"+t+"</"+"script>"},Ze=function(){try{Ge=document.domain&&new ActiveXObject("htmlfile")}catch(t){}var t,e;Ze=Ge?function(t){t.write(Qe("")),t.close();var e=t.parentWindow.Object;return t=null,e}(Ge):((e=_("iframe")).style.display="none",Ye.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(Qe("document.F=Object")),t.close(),t.F);for(var i=It.length;i--;)delete Ze.prototype[It[i]];return Ze()};ot[Xe]=!0;var ti=Object.create||function(t,e){var i;return null!==t?(Je.prototype=V(t),i=new Je,Je.prototype=null,i[Xe]=t):i=Ze(),void 0===e?i:Ke(i,e)},ei=ke("unscopables"),ii=Array.prototype;null==ii[ei]&&L.f(ii,ei,{configurable:!0,value:ti(null)});var ni=function(t){ii[ei][t]=!0},oi=Ct.includes;Mt({target:"Array",proto:!0},{includes:function(t){return oi(this,t,arguments.length>1?arguments[1]:void 0)}}),ni("includes");var ai=Array.isArray||function(t){return"Array"==k(t)},si=function(t){return Object(T(t))},ri=function(t,e,i){var n=A(e);n in t?L.f(t,n,S(0,i)):t[n]=i},li=ke("species"),ci=function(t,e){var i;return ai(t)&&("function"!=typeof(i=t.constructor)||i!==Array&&!ai(i.prototype)?I(i)&&null===(i=i[li])&&(i=void 0):i=void 0),new(void 0===i?Array:i)(0===e?0:e)},hi=ke("species"),ui=function(t){return be>=51||!v((function(){var e=[];return(e.constructor={})[hi]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},di=ke("isConcatSpreadable"),fi=9007199254740991,pi="Maximum allowed index exceeded",gi=be>=51||!v((function(){var t=[];return t[di]=!1,t.concat()[0]!==t})),vi=ui("concat"),bi=function(t){if(!I(t))return!1;var e=t[di];return void 0!==e?!!e:ai(t)};Mt({target:"Array",proto:!0,forced:!gi||!vi},{concat:function(t){var e,i,n,o,a,s=si(this),r=ci(s,0),l=0;for(e=-1,n=arguments.length;e<n;e++)if(bi(a=-1===e?s:arguments[e])){if(l+(o=wt(a.length))>fi)throw TypeError(pi);for(i=0;i<o;i++,l++)i in a&&ri(r,l,a[i])}else{if(l>=fi)throw TypeError(pi);ri(r,l++,a)}return r.length=l,r}});var mi=[].push,yi=function(t){var e=1==t,i=2==t,n=3==t,o=4==t,a=6==t,s=7==t,r=5==t||a;return function(l,c,h,u){for(var d,f,p=si(l),g=C(p),v=function(t,e,i){if(je(t),void 0===e)return t;switch(i){case 0:return function(){return t.call(e)};case 1:return function(i){return t.call(e,i)};case 2:return function(i,n){return t.call(e,i,n)};case 3:return function(i,n,o){return t.call(e,i,n,o)}}return function(){return t.apply(e,arguments)}}(c,h,3),b=wt(g.length),m=0,y=u||ci,w=e?y(l,b):i||s?y(l,0):void 0;b>m;m++)if((r||m in g)&&(f=v(d=g[m],m,p),t))if(e)w[m]=f;else if(f)switch(t){case 3:return!0;case 5:return d;case 6:return m;case 2:mi.call(w,d)}else switch(t){case 4:return!1;case 7:mi.call(w,d)}return a?-1:n||o?o:w}},wi={forEach:yi(0),map:yi(1),filter:yi(2),some:yi(3),every:yi(4),find:yi(5),findIndex:yi(6),filterOut:yi(7)},Si=wi.find,xi="find",ki=!0;xi in[]&&Array(1).find((function(){ki=!1})),Mt({target:"Array",proto:!0,forced:ki},{find:function(t){return Si(this,t,arguments.length>1?arguments[1]:void 0)}}),ni(xi);var Oi=function(t){if(Re(t))throw TypeError("The method doesn't accept regular expressions");return t},Ci=ke("match"),Ti=function(t){var e=/./;try{"/./"[t](e)}catch(i){try{return e[Ci]=!1,"/./"[t](e)}catch(t){}}return!1};Mt({target:"String",proto:!0,forced:!Ti("includes")},{includes:function(t){return!!~String(T(this)).indexOf(Oi(t),arguments.length>1?arguments[1]:void 0)}});var Pi={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},Ii=wi.forEach,Ai=Xt("forEach")?[].forEach:function(t){return Ii(this,t,arguments.length>1?arguments[1]:void 0)};for(var $i in Pi){var Ei=g[$i],Ri=Ei&&Ei.prototype;if(Ri&&Ri.forEach!==Ai)try{H(Ri,"forEach",Ai)}catch(t){Ri.forEach=Ai}}var ji=Kt.trim,_i=g.parseFloat,Ni=1/_i(Ut+"-0")!=-1/0?function(t){var e=ji(String(t)),i=_i(e);return 0===i&&"-"==e.charAt(0)?-0:i}:_i;Mt({global:!0,forced:parseFloat!=Ni},{parseFloat:Ni});var Fi=Ct.indexOf,Di=[].indexOf,Vi=!!Di&&1/[1].indexOf(1,-0)<0,Bi=Xt("indexOf");Mt({target:"Array",proto:!0,forced:Vi||!Bi},{indexOf:function(t){return Vi?Di.apply(this,arguments)||0:Fi(this,t,arguments.length>1?arguments[1]:void 0)}});var Li=[],Hi=Li.sort,Mi=v((function(){Li.sort(void 0)})),Ui=v((function(){Li.sort(null)})),zi=Xt("sort");Mt({target:"Array",proto:!0,forced:Mi||!Ui||!zi},{sort:function(t){return void 0===t?Hi.call(si(this)):Hi.call(si(this),je(t))}});var qi=Math.floor,Wi="".replace,Gi=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,Ki=/\$([$&'`]|\d{1,2})/g,Yi=function(t,e,i,n,o,a){var s=i+t.length,r=n.length,l=Ki;return void 0!==o&&(o=si(o),l=Gi),Wi.call(a,l,(function(a,l){var c;switch(l.charAt(0)){case"$":return"$";case"&":return t;case"`":return e.slice(0,i);case"'":return e.slice(s);case"<":c=o[l.slice(1,-1)];break;default:var h=+l;if(0===h)return a;if(h>r){var u=qi(h/10);return 0===u?a:u<=r?void 0===n[u-1]?l.charAt(1):n[u-1]+l.charAt(1):a}c=n[h-1]}return void 0===c?"":c}))},Xi=Math.max,Ji=Math.min;$e("replace",2,(function(t,e,i,n){var o=n.REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE,a=n.REPLACE_KEEPS_$0,s=o?"$":"$0";return[function(i,n){var o=T(this),a=null==i?void 0:i[t];return void 0!==a?a.call(i,o,n):e.call(String(o),i,n)},function(t,n){if(!o&&a||"string"==typeof n&&-1===n.indexOf(s)){var r=i(e,t,this,n);if(r.done)return r.value}var l=V(t),c=String(this),h="function"==typeof n;h||(n=String(n));var u=l.global;if(u){var d=l.unicode;l.lastIndex=0}for(var f=[];;){var p=Ve(l,c);if(null===p)break;if(f.push(p),!u)break;""===String(p[0])&&(l.lastIndex=De(c,wt(l.lastIndex),d))}for(var g,v="",b=0,m=0;m<f.length;m++){p=f[m];for(var y=String(p[0]),w=Xi(Ji(mt(p.index),c.length),0),S=[],x=1;x<p.length;x++)S.push(void 0===(g=p[x])?g:String(g));var k=p.groups;if(h){var O=[y].concat(S,w,c);void 0!==k&&O.push(k);var C=String(n.apply(void 0,O))}else C=Yi(y,c,w,S,k,n);w>=b&&(v+=c.slice(b,w)+C,b=w+y.length)}return v+c.slice(b)}]}));var Qi=Object.assign,Zi=Object.defineProperty,tn=!Qi||v((function(){if(b&&1!==Qi({b:1},Qi(Zi({},"a",{enumerable:!0,get:function(){Zi(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},i=Symbol(),n="abcdefghijklmnopqrst";return t[i]=7,n.split("").forEach((function(t){e[t]=t})),7!=Qi({},t)[i]||Ue(Qi({},e)).join("")!=n}))?function(t,e){for(var i=si(t),n=arguments.length,o=1,a=Et.f,s=w.f;n>o;)for(var r,l=C(arguments[o++]),c=a?Ue(l).concat(a(l)):Ue(l),h=c.length,u=0;h>u;)r=c[u++],b&&!s.call(l,r)||(i[r]=l[r]);return i}:Qi;Mt({target:"Object",stat:!0,forced:Object.assign!==tn},{assign:tn});var en=wi.filter,nn=ui("filter");Mt({target:"Array",proto:!0,forced:!nn},{filter:function(t){return en(this,t,arguments.length>1?arguments[1]:void 0)}});var on=Object.is||function(t,e){return t===e?0!==t||1/t==1/e:t!=t&&e!=e};$e("search",1,(function(t,e,i){return[function(e){var i=T(this),n=null==e?void 0:e[t];return void 0!==n?n.call(e,i):new RegExp(e)[t](String(i))},function(t){var n=i(e,t,this);if(n.done)return n.value;var o=V(t),a=String(this),s=o.lastIndex;on(s,0)||(o.lastIndex=0);var r=Ve(o,a);return on(o.lastIndex,s)||(o.lastIndex=s),null===r?-1:r.index}]}));var an=Kt.trim,sn=g.parseInt,rn=/^[+-]?0[Xx]/,ln=8!==sn(Ut+"08")||22!==sn(Ut+"0x16")?function(t,e){var i=an(String(t));return sn(i,e>>>0||(rn.test(i)?16:10))}:sn;Mt({global:!0,forced:parseInt!=ln},{parseInt:ln});var cn=wi.map,hn=ui("map");Mt({target:"Array",proto:!0,forced:!hn},{map:function(t){return cn(this,t,arguments.length>1?arguments[1]:void 0)}});var un=wi.findIndex,dn="findIndex",fn=!0;dn in[]&&Array(1).findIndex((function(){fn=!1})),Mt({target:"Array",proto:!0,forced:fn},{findIndex:function(t){return un(this,t,arguments.length>1?arguments[1]:void 0)}}),ni(dn);var pn=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,i={};try{(t=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set).call(i,[]),e=i instanceof Array}catch(t){}return function(i,n){return V(i),function(t){if(!I(t)&&null!==t)throw TypeError("Can't set "+String(t)+" as a prototype")}(n),e?t.call(i,n):i.__proto__=n,i}}():void 0),gn=function(t,e,i){var n,o;return pn&&"function"==typeof(n=e.constructor)&&n!==i&&I(o=n.prototype)&&o!==i.prototype&&pn(t,o),t},vn=ke("species"),bn=L.f,mn=$t.f,yn=ut.set,wn=ke("match"),Sn=g.RegExp,xn=Sn.prototype,kn=/a/g,On=/a/g,Cn=new Sn(kn)!==kn,Tn=ie.UNSUPPORTED_Y;if(b&&Lt("RegExp",!Cn||Tn||v((function(){return On[wn]=!1,Sn(kn)!=kn||Sn(On)==On||"/a/i"!=Sn(kn,"i")})))){for(var Pn=function(t,e){var i,n=this instanceof Pn,o=Re(t),a=void 0===e;if(!n&&o&&t.constructor===Pn&&a)return t;Cn?o&&!a&&(t=t.source):t instanceof Pn&&(a&&(e=te.call(t)),t=t.source),Tn&&(i=!!e&&e.indexOf("y")>-1)&&(e=e.replace(/y/g,""));var s=gn(Cn?new Sn(t,e):Sn(t,e),n?this:xn,Pn);return Tn&&i&&yn(s,{sticky:i}),s},In=function(t){t in Pn||bn(Pn,t,{configurable:!0,get:function(){return Sn[t]},set:function(e){Sn[t]=e}})},An=mn(Sn),$n=0;An.length>$n;)In(An[$n++]);xn.constructor=Pn,Pn.prototype=xn,dt(g,"RegExp",Pn)}!function(t){var e=gt(t),i=L.f;b&&e&&!e[vn]&&i(e,vn,{configurable:!0,get:function(){return this}})}("RegExp");var En="toString",Rn=RegExp.prototype,jn=Rn.toString,_n=v((function(){return"/a/b"!=jn.call({source:"a",flags:"b"})})),Nn=jn.name!=En;(_n||Nn)&&dt(RegExp.prototype,En,(function(){var t=V(this),e=String(t.source),i=t.flags;return"/"+e+"/"+String(void 0===i&&t instanceof RegExp&&!("flags"in Rn)?te.call(t):i)}),{unsafe:!0});var Fn={};Fn[ke("toStringTag")]="z";var Dn="[object z]"===String(Fn),Vn=ke("toStringTag"),Bn="Arguments"==k(function(){return arguments}()),Ln=Dn?k:function(t){var e,i,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(i=function(t,e){try{return t[e]}catch(t){}}(e=Object(t),Vn))?i:Bn?k(e):"Object"==(n=k(e))&&"function"==typeof e.callee?"Arguments":n},Hn=Dn?{}.toString:function(){return"[object "+Ln(this)+"]"};Dn||dt(Object.prototype,"toString",Hn,{unsafe:!0});var Mn=ui("slice"),Un=ke("species"),zn=[].slice,qn=Math.max;Mt({target:"Array",proto:!0,forced:!Mn},{slice:function(t,e){var i,n,o,a=P(this),s=wt(a.length),r=kt(t,s),l=kt(void 0===e?s:e,s);if(ai(a)&&("function"!=typeof(i=a.constructor)||i!==Array&&!ai(i.prototype)?I(i)&&null===(i=i[Un])&&(i=void 0):i=void 0,i===Array||void 0===i))return zn.call(a,r,l);for(n=new(void 0===i?Array:i)(qn(l-r,0)),o=0;r<l;r++,o++)r in a&&ri(n,o,a[r]);return n.length=o,n}});var Wn,Gn,Kn,Yn=!v((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})),Xn=nt("IE_PROTO"),Jn=Object.prototype,Qn=Yn?Object.getPrototypeOf:function(t){return t=si(t),E(t,Xn)?t[Xn]:"function"==typeof t.constructor&&t instanceof t.constructor?t.constructor.prototype:t instanceof Object?Jn:null},Zn=ke("iterator"),to=!1;[].keys&&("next"in(Kn=[].keys())?(Gn=Qn(Qn(Kn)))!==Object.prototype&&(Wn=Gn):to=!0),(null==Wn||v((function(){var t={};return Wn[Zn].call(t)!==t})))&&(Wn={}),E(Wn,Zn)||H(Wn,Zn,(function(){return this}));var eo={IteratorPrototype:Wn,BUGGY_SAFARI_ITERATORS:to},io=L.f,no=ke("toStringTag"),oo=function(t,e,i){t&&!E(t=i?t:t.prototype,no)&&io(t,no,{configurable:!0,value:e})},ao=eo.IteratorPrototype,so=eo.IteratorPrototype,ro=eo.BUGGY_SAFARI_ITERATORS,lo=ke("iterator"),co="keys",ho="values",uo="entries",fo=function(){return this},po="Array Iterator",go=ut.set,vo=ut.getterFor(po),bo=function(t,e,i,n,o,a,s){!function(t,e,i){var n=e+" Iterator";t.prototype=ti(ao,{next:S(1,i)}),oo(t,n,!1)}(i,e,n);var r,l,c,h=function(t){if(t===o&&g)return g;if(!ro&&t in f)return f[t];switch(t){case co:case ho:case uo:return function(){return new i(this,t)}}return function(){return new i(this)}},u=e+" Iterator",d=!1,f=t.prototype,p=f[lo]||f["@@iterator"]||o&&f[o],g=!ro&&p||h(o),v="Array"==e&&f.entries||p;if(v&&(r=Qn(v.call(new t)),so!==Object.prototype&&r.next&&(Qn(r)!==so&&(pn?pn(r,so):"function"!=typeof r[lo]&&H(r,lo,fo)),oo(r,u,!0))),o==ho&&p&&p.name!==ho&&(d=!0,g=function(){return p.call(this)}),f[lo]!==g&&H(f,lo,g),o)if(l={values:h(ho),keys:a?g:h(co),entries:h(uo)},s)for(c in l)(ro||d||!(c in f))&&dt(f,c,l[c]);else Mt({target:e,proto:!0,forced:ro||d},l);return l}(Array,"Array",(function(t,e){go(this,{type:po,target:P(t),index:0,kind:e})}),(function(){var t=vo(this),e=t.target,i=t.kind,n=t.index++;return!e||n>=e.length?(t.target=void 0,{value:void 0,done:!0}):"keys"==i?{value:n,done:!1}:"values"==i?{value:e[n],done:!1}:{value:[n,e[n]],done:!1}}),"values");ni("keys"),ni("values"),ni("entries");var mo=ke("iterator"),yo=ke("toStringTag"),wo=bo.values;for(var So in Pi){var xo=g[So],ko=xo&&xo.prototype;if(ko){if(ko[mo]!==wo)try{H(ko,mo,wo)}catch(t){ko[mo]=wo}if(ko[yo]||H(ko,yo,So),Pi[So])for(var Oo in bo)if(ko[Oo]!==bo[Oo])try{H(ko,Oo,bo[Oo])}catch(t){ko[Oo]=bo[Oo]}}}var Co=ui("splice"),To=Math.max,Po=Math.min,Io=9007199254740991,Ao="Maximum allowed length exceeded";Mt({target:"Array",proto:!0,forced:!Co},{splice:function(t,e){var i,n,o,a,s,r,l=si(this),c=wt(l.length),h=kt(t,c),u=arguments.length;if(0===u?i=n=0:1===u?(i=0,n=c-h):(i=u-2,n=Po(To(mt(e),0),c-h)),c+i-n>Io)throw TypeError(Ao);for(o=ci(l,n),a=0;a<n;a++)(s=h+a)in l&&ri(o,a,l[s]);if(o.length=n,i<n){for(a=h;a<c-n;a++)r=a+i,(s=a+n)in l?l[r]=l[s]:delete l[r];for(a=c;a>c-n+i;a--)delete l[a-1]}else if(i>n)for(a=c-n;a>h;a--)r=a+i-1,(s=a+n-1)in l?l[r]=l[s]:delete l[r];for(a=0;a<i;a++)l[a+h]=arguments[a+2];return l.length=c-n+i,o}});var $o=$t.f,Eo=D.f,Ro=L.f,jo=Kt.trim,_o="Number",No=g.Number,Fo=No.prototype,Do=k(ti(Fo))==_o,Vo=function(t){var e,i,n,o,a,s,r,l,c=A(t,!1);if("string"==typeof c&&c.length>2)if(43===(e=(c=jo(c)).charCodeAt(0))||45===e){if(88===(i=c.charCodeAt(2))||120===i)return NaN}else if(48===e){switch(c.charCodeAt(1)){case 66:case 98:n=2,o=49;break;case 79:case 111:n=8,o=55;break;default:return+c}for(s=(a=c.slice(2)).length,r=0;r<s;r++)if((l=a.charCodeAt(r))<48||l>o)return NaN;return parseInt(a,n)}return+c};if(Lt(_o,!No(" 0o1")||!No("0b1")||No("+0x1"))){for(var Bo,Lo=function(t){var e=arguments.length<1?0:t,i=this;return i instanceof Lo&&(Do?v((function(){Fo.valueOf.call(i)})):k(i)!=_o)?gn(new No(Vo(e)),i,Lo):Vo(e)},Ho=b?$o(No):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger,fromString,range".split(","),Mo=0;Ho.length>Mo;Mo++)E(No,Bo=Ho[Mo])&&!E(Lo,Bo)&&Ro(Lo,Bo,Eo(No,Bo));Lo.prototype=Fo,Fo.constructor=Lo,dt(g,_o,Lo)}var Uo=[].reverse,zo=[1,2];Mt({target:"Array",proto:!0,forced:String(zo)===String(zo.reverse())},{reverse:function(){return ai(this)&&(this.length=this.length),Uo.call(this)}});var qo=4;try{var Wo=i.default.fn.dropdown.Constructor.VERSION;void 0!==Wo&&(qo=parseInt(Wo,10))}catch(t){}try{var Go=bootstrap.Tooltip.VERSION;void 0!==Go&&(qo=parseInt(Go,10))}catch(t){}var Ko={3:{iconsPrefix:"glyphicon",icons:{paginationSwitchDown:"glyphicon-collapse-down icon-chevron-down",paginationSwitchUp:"glyphicon-collapse-up icon-chevron-up",refresh:"glyphicon-refresh icon-refresh",toggleOff:"glyphicon-list-alt icon-list-alt",toggleOn:"glyphicon-list-alt icon-list-alt",columns:"glyphicon-th icon-th",detailOpen:"glyphicon-plus icon-plus",detailClose:"glyphicon-minus icon-minus",fullscreen:"glyphicon-fullscreen",search:"glyphicon-search",clearSearch:"glyphicon-trash"},classes:{buttonsPrefix:"btn",buttons:"default",buttonsGroup:"btn-group",buttonsDropdown:"btn-group",pull:"pull",inputGroup:"input-group",inputPrefix:"input-",input:"form-control",paginationDropdown:"btn-group dropdown",dropup:"dropup",dropdownActive:"active",paginationActive:"active",buttonActive:"active"},html:{toolbarDropdown:['<ul class="dropdown-menu" role="menu">',"</ul>"],toolbarDropdownItem:'<li class="dropdown-item-marker" role="menuitem"><label>%s</label></li>',toolbarDropdownSeparator:'<li class="divider"></li>',pageDropdown:['<ul class="dropdown-menu" role="menu">',"</ul>"],pageDropdownItem:'<li role="menuitem" class="%s"><a href="#">%s</a></li>',dropdownCaret:'<span class="caret"></span>',pagination:['<ul class="pagination%s">',"</ul>"],paginationItem:'<li class="page-item%s"><a class="page-link" aria-label="%s" href="javascript:void(0)">%s</a></li>',icon:'<i class="%s %s"></i>',inputGroup:'<div class="input-group">%s<span class="input-group-btn">%s</span></div>',searchInput:'<input class="%s%s" type="text" placeholder="%s">',searchButton:'<button class="%s" type="button" name="search" title="%s">%s %s</button>',searchClearButton:'<button class="%s" type="button" name="clearSearch" title="%s">%s %s</button>'}},4:{iconsPrefix:"fa",icons:{paginationSwitchDown:"fa-caret-square-down",paginationSwitchUp:"fa-caret-square-up",refresh:"fa-sync",toggleOff:"fa-toggle-off",toggleOn:"fa-toggle-on",columns:"fa-th-list",detailOpen:"fa-plus",detailClose:"fa-minus",fullscreen:"fa-arrows-alt",search:"fa-search",clearSearch:"fa-trash"},classes:{buttonsPrefix:"btn",buttons:"secondary",buttonsGroup:"btn-group",buttonsDropdown:"btn-group",pull:"float",inputGroup:"btn-group",inputPrefix:"form-control-",input:"form-control",paginationDropdown:"btn-group dropdown",dropup:"dropup",dropdownActive:"active",paginationActive:"active",buttonActive:"active"},html:{toolbarDropdown:['<div class="dropdown-menu dropdown-menu-right">',"</div>"],toolbarDropdownItem:'<label class="dropdown-item dropdown-item-marker">%s</label>',pageDropdown:['<div class="dropdown-menu">',"</div>"],pageDropdownItem:'<a class="dropdown-item %s" href="#">%s</a>',toolbarDropdownSeparator:'<div class="dropdown-divider"></div>',dropdownCaret:'<span class="caret"></span>',pagination:['<ul class="pagination%s">',"</ul>"],paginationItem:'<li class="page-item%s"><a class="page-link" aria-label="%s" href="javascript:void(0)">%s</a></li>',icon:'<i class="%s %s"></i>',inputGroup:'<div class="input-group">%s<div class="input-group-append">%s</div></div>',searchInput:'<input class="%s%s" type="text" placeholder="%s">',searchButton:'<button class="%s" type="button" name="search" title="%s">%s %s</button>',searchClearButton:'<button class="%s" type="button" name="clearSearch" title="%s">%s %s</button>'}},5:{iconsPrefix:"bi",icons:{paginationSwitchDown:"bi-caret-down-square",paginationSwitchUp:"bi-caret-up-square",refresh:"bi-arrow-clockwise",toggleOff:"bi-toggle-off",toggleOn:"bi-toggle-on",columns:"bi-list-ul",detailOpen:"bi-plus",detailClose:"bi-dash",fullscreen:"bi-arrows-move",search:"bi-search",clearSearch:"bi-trash"},classes:{buttonsPrefix:"btn",buttons:"secondary",buttonsGroup:"btn-group",buttonsDropdown:"btn-group",pull:"float",inputGroup:"btn-group",inputPrefix:"form-control-",input:"form-control",paginationDropdown:"btn-group dropdown",dropup:"dropup",dropdownActive:"active",paginationActive:"active",buttonActive:"active"},html:{dataToggle:"data-bs-toggle",toolbarDropdown:['<div class="dropdown-menu dropdown-menu-right">',"</div>"],toolbarDropdownItem:'<label class="dropdown-item dropdown-item-marker">%s</label>',pageDropdown:['<div class="dropdown-menu">',"</div>"],pageDropdownItem:'<a class="dropdown-item %s" href="#">%s</a>',toolbarDropdownSeparator:'<div class="dropdown-divider"></div>',dropdownCaret:'<span class="caret"></span>',pagination:['<ul class="pagination%s">',"</ul>"],paginationItem:'<li class="page-item%s"><a class="page-link" aria-label="%s" href="javascript:void(0)">%s</a></li>',icon:'<i class="%s %s"></i>',inputGroup:'<div class="input-group">%s%s</div>',searchInput:'<input class="%s%s" type="text" placeholder="%s">',searchButton:'<button class="%s" type="button" name="search" title="%s">%s %s</button>',searchClearButton:'<button class="%s" type="button" name="clearSearch" title="%s">%s %s</button>'}}}[qo],Yo={height:void 0,classes:"table table-bordered table-hover",buttons:{},theadClasses:"",headerStyle:function(t){return{}},rowStyle:function(t,e){return{}},rowAttributes:function(t,e){return{}},undefinedText:"-",locale:void 0,virtualScroll:!1,virtualScrollItemHeight:void 0,sortable:!0,sortClass:void 0,silentSort:!0,sortName:void 0,sortOrder:void 0,sortReset:!1,sortStable:!1,rememberOrder:!1,serverSort:!0,customSort:void 0,columns:[[]],data:[],url:void 0,method:"get",cache:!0,contentType:"application/json",dataType:"json",ajax:void 0,ajaxOptions:{},queryParams:function(t){return t},queryParamsType:"limit",responseHandler:function(t){return t},totalField:"total",totalNotFilteredField:"totalNotFiltered",dataField:"rows",footerField:"footer",pagination:!1,paginationParts:["pageInfo","pageSize","pageList"],showExtendedPagination:!1,paginationLoop:!0,sidePagination:"client",totalRows:0,totalNotFiltered:0,pageNumber:1,pageSize:10,pageList:[10,25,50,100],paginationHAlign:"right",paginationVAlign:"bottom",paginationDetailHAlign:"left",paginationPreText:"&lsaquo;",paginationNextText:"&rsaquo;",paginationSuccessivelySize:5,paginationPagesBySide:1,paginationUseIntermediate:!1,search:!1,searchHighlight:!1,searchOnEnterKey:!1,strictSearch:!1,regexSearch:!1,searchSelector:!1,visibleSearch:!1,showButtonIcons:!0,showButtonText:!1,showSearchButton:!1,showSearchClearButton:!1,trimOnSearch:!0,searchAlign:"right",searchTimeOut:500,searchText:"",customSearch:void 0,showHeader:!0,showFooter:!1,footerStyle:function(t){return{}},searchAccentNeutralise:!1,showColumns:!1,showColumnsToggleAll:!1,showColumnsSearch:!1,minimumCountColumns:1,showPaginationSwitch:!1,showRefresh:!1,showToggle:!1,showFullscreen:!1,smartDisplay:!0,escape:!1,filterOptions:{filterAlgorithm:"and"},idField:void 0,selectItemName:"btSelectItem",clickToSelect:!1,ignoreClickToSelectOn:function(t){var e=t.tagName;return["A","BUTTON"].includes(e)},singleSelect:!1,checkboxHeader:!0,maintainMetaData:!1,multipleSelectRow:!1,uniqueId:void 0,cardView:!1,detailView:!1,detailViewIcon:!0,detailViewByClick:!1,detailViewAlign:"left",detailFormatter:function(t,e){return""},detailFilter:function(t,e){return!0},toolbar:void 0,toolbarAlign:"left",buttonsToolbar:void 0,buttonsAlign:"right",buttonsOrder:["paginationSwitch","refresh","toggle","fullscreen","columns"],buttonsPrefix:Ko.classes.buttonsPrefix,buttonsClass:Ko.classes.buttons,icons:Ko.icons,iconSize:void 0,iconsPrefix:Ko.iconsPrefix,loadingFontSize:"auto",loadingTemplate:function(t){return'<span class="loading-wrap">\n      <span class="loading-text">'.concat(t,'</span>\n      <span class="animation-wrap"><span class="animation-dot"></span></span>\n      </span>\n    ')},onAll:function(t,e){return!1},onClickCell:function(t,e,i,n){return!1},onDblClickCell:function(t,e,i,n){return!1},onClickRow:function(t,e){return!1},onDblClickRow:function(t,e){return!1},onSort:function(t,e){return!1},onCheck:function(t){return!1},onUncheck:function(t){return!1},onCheckAll:function(t){return!1},onUncheckAll:function(t){return!1},onCheckSome:function(t){return!1},onUncheckSome:function(t){return!1},onLoadSuccess:function(t){return!1},onLoadError:function(t){return!1},onColumnSwitch:function(t,e){return!1},onPageChange:function(t,e){return!1},onSearch:function(t){return!1},onToggle:function(t){return!1},onPreBody:function(t){return!1},onPostBody:function(){return!1},onPostHeader:function(){return!1},onPostFooter:function(){return!1},onExpandRow:function(t,e,i){return!1},onCollapseRow:function(t,e){return!1},onRefreshOptions:function(t){return!1},onRefresh:function(t){return!1},onResetView:function(){return!1},onScrollBody:function(){return!1},onTogglePagination:function(t){return!1}},Xo={formatLoadingMessage:function(){return"Loading, please wait"},formatRecordsPerPage:function(t){return"".concat(t," rows per page")},formatShowingRows:function(t,e,i,n){return void 0!==n&&n>0&&n>i?"Showing ".concat(t," to ").concat(e," of ").concat(i," rows (filtered from ").concat(n," total rows)"):"Showing ".concat(t," to ").concat(e," of ").concat(i," rows")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatSearch:function(){return"Search"},formatClearSearch:function(){return"Clear Search"},formatNoMatches:function(){return"No matching records found"},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Refresh"},formatToggle:function(){return"Toggle"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Columns"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"All"}},Jo={field:void 0,title:void 0,titleTooltip:void 0,class:void 0,width:void 0,widthUnit:"px",rowspan:void 0,colspan:void 0,align:void 0,halign:void 0,falign:void 0,valign:void 0,cellStyle:void 0,radio:!1,checkbox:!1,checkboxEnabled:!0,clickToSelect:!0,showSelectTitle:!1,sortable:!1,sortName:void 0,order:"asc",sorter:void 0,visible:!0,switchable:!0,cardVisible:!0,searchable:!0,formatter:void 0,footerFormatter:void 0,detailFormatter:void 0,searchFormatter:!0,searchHighlightFormatter:!1,escape:!1,events:void 0};Object.assign(Yo,Xo);var Qo={VERSION:"1.19.1",THEME:"bootstrap".concat(qo),CONSTANTS:Ko,DEFAULTS:Yo,COLUMN_DEFAULTS:Jo,METHODS:["getOptions","refreshOptions","getData","getSelections","load","append","prepend","remove","removeAll","insertRow","updateRow","getRowByUniqueId","updateByUniqueId","removeByUniqueId","updateCell","updateCellByUniqueId","showRow","hideRow","getHiddenRows","showColumn","hideColumn","getVisibleColumns","getHiddenColumns","showAllColumns","hideAllColumns","mergeCells","checkAll","uncheckAll","checkInvert","check","uncheck","checkBy","uncheckBy","refresh","destroy","resetView","showLoading","hideLoading","togglePagination","toggleFullscreen","toggleView","resetSearch","filterBy","scrollTo","getScrollPosition","selectPage","prevPage","nextPage","toggleDetailView","expandRow","collapseRow","expandRowByUniqueId","collapseRowByUniqueId","expandAllRows","collapseAllRows","updateColumnTitle","updateFormatText"],EVENTS:{"all.bs.table":"onAll","click-row.bs.table":"onClickRow","dbl-click-row.bs.table":"onDblClickRow","click-cell.bs.table":"onClickCell","dbl-click-cell.bs.table":"onDblClickCell","sort.bs.table":"onSort","check.bs.table":"onCheck","uncheck.bs.table":"onUncheck","check-all.bs.table":"onCheckAll","uncheck-all.bs.table":"onUncheckAll","check-some.bs.table":"onCheckSome","uncheck-some.bs.table":"onUncheckSome","load-success.bs.table":"onLoadSuccess","load-error.bs.table":"onLoadError","column-switch.bs.table":"onColumnSwitch","page-change.bs.table":"onPageChange","search.bs.table":"onSearch","toggle.bs.table":"onToggle","pre-body.bs.table":"onPreBody","post-body.bs.table":"onPostBody","post-header.bs.table":"onPostHeader","post-footer.bs.table":"onPostFooter","expand-row.bs.table":"onExpandRow","collapse-row.bs.table":"onCollapseRow","refresh-options.bs.table":"onRefreshOptions","reset-view.bs.table":"onResetView","refresh.bs.table":"onRefresh","scroll-body.bs.table":"onScrollBody","toggle-pagination.bs.table":"onTogglePagination","virtual-scroll.bs.table":"onVirtualScroll"},LOCALES:{en:Xo,"en-US":Xo}},Zo=v((function(){Ue(1)}));Mt({target:"Object",stat:!0,forced:Zo},{keys:function(t){return Ue(si(t))}}),$e("match",1,(function(t,e,i){return[function(e){var i=T(this),n=null==e?void 0:e[t];return void 0!==n?n.call(e,i):new RegExp(e)[t](String(i))},function(t){var n=i(e,t,this);if(n.done)return n.value;var o=V(t),a=String(this);if(!o.global)return Ve(o,a);var s=o.unicode;o.lastIndex=0;for(var r,l=[],c=0;null!==(r=Ve(o,a));){var h=String(r[0]);l[c]=h,""===h&&(o.lastIndex=De(a,wt(o.lastIndex),s)),c++}return 0===c?null:l}]}));var ta,ea=D.f,ia="".startsWith,na=Math.min,oa=Ti("startsWith"),aa=!(oa||(ta=ea(String.prototype,"startsWith"),!ta||ta.writable));Mt({target:"String",proto:!0,forced:!aa&&!oa},{startsWith:function(t){var e=String(T(this));Oi(t);var i=wt(na(arguments.length>1?arguments[1]:void 0,e.length)),n=String(t);return ia?ia.call(e,n,i):e.slice(i,i+n.length)===n}});var sa=D.f,ra="".endsWith,la=Math.min,ca=Ti("endsWith"),ha=!ca&&!!function(){var t=sa(String.prototype,"endsWith");return t&&!t.writable}();Mt({target:"String",proto:!0,forced:!ha&&!ca},{endsWith:function(t){var e=String(T(this));Oi(t);var i=arguments.length>1?arguments[1]:void 0,n=wt(e.length),o=void 0===i?n:la(wt(i),n),a=String(t);return ra?ra.call(e,a,o):e.slice(o-a.length,o)===a}});var ua={getSearchInput:function(t){return"string"==typeof t.options.searchSelector?i.default(t.options.searchSelector):t.$toolbar.find(".search input")},sprintf:function(t){for(var e=arguments.length,i=new Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];var o=!0,a=0,s=t.replace(/%s/g,(function(){var t=i[a++];return void 0===t?(o=!1,""):t}));return o?s:""},isObject:function(t){return t instanceof Object&&!Array.isArray(t)},isEmptyObject:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return 0===Object.entries(t).length&&t.constructor===Object},isNumeric:function(t){return!isNaN(parseFloat(t))&&isFinite(t)},getFieldTitle:function(t,e){var i,n=u(t);try{for(n.s();!(i=n.n()).done;){var o=i.value;if(o.field===e)return o.title}}catch(t){n.e(t)}finally{n.f()}return""},setFieldIndex:function(t){var e,i=0,n=[],o=u(t[0]);try{for(o.s();!(e=o.n()).done;){i+=e.value.colspan||1}}catch(t){o.e(t)}finally{o.f()}for(var a=0;a<t.length;a++){n[a]=[];for(var s=0;s<i;s++)n[a][s]=!1}for(var r=0;r<t.length;r++){var l,c=u(t[r]);try{for(c.s();!(l=c.n()).done;){var h=l.value,d=h.rowspan||1,f=h.colspan||1,p=n[r].indexOf(!1);h.colspanIndex=p,1===f?(h.fieldIndex=p,void 0===h.field&&(h.field=p)):h.colspanGroup=h.colspan;for(var g=0;g<d;g++)for(var v=0;v<f;v++)n[r+g][p+v]=!0}}catch(t){c.e(t)}finally{c.f()}}},normalizeAccent:function(t){return"string"!=typeof t?t:t.normalize("NFD").replace(/[\u0300-\u036f]/g,"")},updateFieldGroup:function(t){var e,i,n=(e=[]).concat.apply(e,l(t)),o=u(t);try{for(o.s();!(i=o.n()).done;){var a,s=u(i.value);try{for(s.s();!(a=s.n()).done;){var r=a.value;if(r.colspanGroup>1){for(var c=0,h=function(t){n.find((function(e){return e.fieldIndex===t})).visible&&c++},d=r.colspanIndex;d<r.colspanIndex+r.colspanGroup;d++)h(d);r.colspan=c,r.visible=c>0}}}catch(t){s.e(t)}finally{s.f()}}}catch(t){o.e(t)}finally{o.f()}},getScrollBarWidth:function(){if(void 0===this.cachedWidth){var t=i.default("<div/>").addClass("fixed-table-scroll-inner"),e=i.default("<div/>").addClass("fixed-table-scroll-outer");e.append(t),i.default("body").append(e);var n=t[0].offsetWidth;e.css("overflow","scroll");var o=t[0].offsetWidth;n===o&&(o=e[0].clientWidth),e.remove(),this.cachedWidth=n-o}return this.cachedWidth},calculateObjectValue:function(t,e,i,o){var a=e;if("string"==typeof e){var s=e.split(".");if(s.length>1){a=window;var r,c=u(s);try{for(c.s();!(r=c.n()).done;){a=a[r.value]}}catch(t){c.e(t)}finally{c.f()}}else a=window[e]}return null!==a&&"object"===n(a)?a:"function"==typeof a?a.apply(t,i||[]):!a&&"string"==typeof e&&this.sprintf.apply(this,[e].concat(l(i)))?this.sprintf.apply(this,[e].concat(l(i))):o},compareObjects:function(t,e,i){var n=Object.keys(t),o=Object.keys(e);if(i&&n.length!==o.length)return!1;for(var a=0,s=n;a<s.length;a++){var r=s[a];if(o.includes(r)&&t[r]!==e[r])return!1}return!0},regexCompare:function(t,e){try{var i=e.match(/^\/(.*?)\/([gim]*)$/);if(-1!==t.toString().search(i?new RegExp(i[1],i[2]):new RegExp(e,"gim")))return!0}catch(t){return!1}},escapeHTML:function(t){return t?t.toString().replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#39;"):t},unescapeHTML:function(t){return t?t.toString().replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&#39;/g,"'"):t},getRealDataAttr:function(t){for(var e=0,i=Object.entries(t);e<i.length;e++){var n=r(i[e],2),o=n[0],a=n[1],s=o.split(/(?=[A-Z])/).join("-").toLowerCase();s!==o&&(t[s]=a,delete t[o])}return t},getItemField:function(t,e,i){var n=t;if("string"!=typeof e||t.hasOwnProperty(e))return i?this.escapeHTML(t[e]):t[e];var o,a=u(e.split("."));try{for(a.s();!(o=a.n()).done;){var s=o.value;n=n&&n[s]}}catch(t){a.e(t)}finally{a.f()}return i?this.escapeHTML(n):n},isIEBrowser:function(){return navigator.userAgent.includes("MSIE ")||/Trident.*rv:11\./.test(navigator.userAgent)},findIndex:function(t,e){var i,n=u(t);try{for(n.s();!(i=n.n()).done;){var o=i.value;if(JSON.stringify(o)===JSON.stringify(e))return t.indexOf(o)}}catch(t){n.e(t)}finally{n.f()}return-1},trToData:function(t,e){var n=this,o=[],a=[];return e.each((function(e,s){var r=i.default(s),l={};l._id=r.attr("id"),l._class=r.attr("class"),l._data=n.getRealDataAttr(r.data()),l._style=r.attr("style"),r.find(">td,>th").each((function(o,s){for(var r=i.default(s),c=+r.attr("colspan")||1,h=+r.attr("rowspan")||1,u=o;a[e]&&a[e][u];u++);for(var d=u;d<u+c;d++)for(var f=e;f<e+h;f++)a[f]||(a[f]=[]),a[f][d]=!0;var p=t[u].field;l[p]=r.html().trim(),l["_".concat(p,"_id")]=r.attr("id"),l["_".concat(p,"_class")]=r.attr("class"),l["_".concat(p,"_rowspan")]=r.attr("rowspan"),l["_".concat(p,"_colspan")]=r.attr("colspan"),l["_".concat(p,"_title")]=r.attr("title"),l["_".concat(p,"_data")]=n.getRealDataAttr(r.data()),l["_".concat(p,"_style")]=r.attr("style")})),o.push(l)})),o},sort:function(t,e,i,n,o,a){return null==t&&(t=""),null==e&&(e=""),n&&t===e&&(t=o,e=a),this.isNumeric(t)&&this.isNumeric(e)?(t=parseFloat(t))<(e=parseFloat(e))?-1*i:t>e?i:0:t===e?0:("string"!=typeof t&&(t=t.toString()),-1===t.localeCompare(e)?-1*i:i)},getEventName:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return e=e||"".concat(+new Date).concat(~~(1e6*Math.random())),"".concat(t,"-").concat(e)},hasDetailViewIcon:function(t){return t.detailView&&t.detailViewIcon&&!t.cardView},getDetailViewIndexOffset:function(t){return this.hasDetailViewIcon(t)&&"right"!==t.detailViewAlign?1:0},checkAutoMergeCells:function(t){var e,i=u(t);try{for(i.s();!(e=i.n()).done;)for(var n=e.value,o=0,a=Object.keys(n);o<a.length;o++){var s=a[o];if(s.startsWith("_")&&(s.endsWith("_rowspan")||s.endsWith("_colspan")))return!0}}catch(t){i.e(t)}finally{i.f()}return!1},deepCopy:function(t){return void 0===t?t:i.default.extend(!0,Array.isArray(t)?[]:{},t)}},da=function(){function t(e){var i=this;o(this,t),this.rows=e.rows,this.scrollEl=e.scrollEl,this.contentEl=e.contentEl,this.callback=e.callback,this.itemHeight=e.itemHeight,this.cache={},this.scrollTop=this.scrollEl.scrollTop,this.initDOM(this.rows,e.fixedScroll),this.scrollEl.scrollTop=this.scrollTop,this.lastCluster=0;var n=function(){i.lastCluster!==(i.lastCluster=i.getNum())&&(i.initDOM(i.rows),i.callback(i.startIndex,i.endIndex))};this.scrollEl.addEventListener("scroll",n,!1),this.destroy=function(){i.contentEl.innerHtml="",i.scrollEl.removeEventListener("scroll",n,!1)}}return s(t,[{key:"initDOM",value:function(t,e){void 0===this.clusterHeight&&(this.cache.scrollTop=this.scrollEl.scrollTop,this.cache.data=this.contentEl.innerHTML=t[0]+t[0]+t[0],this.getRowsHeight(t));var i=this.initData(t,this.getNum(e)),n=i.rows.join(""),o=this.checkChanges("data",n),a=this.checkChanges("top",i.topOffset),s=this.checkChanges("bottom",i.bottomOffset),r=[];o&&a?(i.topOffset&&r.push(this.getExtra("top",i.topOffset)),r.push(n),i.bottomOffset&&r.push(this.getExtra("bottom",i.bottomOffset)),this.startIndex=i.start,this.endIndex=i.end,this.contentEl.innerHTML=r.join(""),e&&(this.contentEl.scrollTop=this.cache.scrollTop)):s&&(this.contentEl.lastChild.style.height="".concat(i.bottomOffset,"px"))}},{key:"getRowsHeight",value:function(){if(void 0===this.itemHeight){var t=this.contentEl.children,e=t[Math.floor(t.length/2)];this.itemHeight=e.offsetHeight}this.blockHeight=50*this.itemHeight,this.clusterRows=200,this.clusterHeight=4*this.blockHeight}},{key:"getNum",value:function(t){return this.scrollTop=t?this.cache.scrollTop:this.scrollEl.scrollTop,Math.floor(this.scrollTop/(this.clusterHeight-this.blockHeight))||0}},{key:"initData",value:function(t,e){if(t.length<50)return{topOffset:0,bottomOffset:0,rowsAbove:0,rows:t};var i=Math.max((this.clusterRows-50)*e,0),n=i+this.clusterRows,o=Math.max(i*this.itemHeight,0),a=Math.max((t.length-n)*this.itemHeight,0),s=[],r=i;o<1&&r++;for(var l=i;l<n;l++)t[l]&&s.push(t[l]);return{start:i,end:n,topOffset:o,bottomOffset:a,rowsAbove:r,rows:s}}},{key:"checkChanges",value:function(t,e){var i=e!==this.cache[t];return this.cache[t]=e,i}},{key:"getExtra",value:function(t,e){var i=document.createElement("tr");return i.className="virtual-scroll-".concat(t),e&&(i.style.height="".concat(e,"px")),i.outerHTML}}]),t}(),fa=function(){function t(e,n){o(this,t),this.options=n,this.$el=i.default(e),this.$el_=this.$el.clone(),this.timeoutId_=0,this.timeoutFooter_=0}return s(t,[{key:"init",value:function(){this.initConstants(),this.initLocale(),this.initContainer(),this.initTable(),this.initHeader(),this.initData(),this.initHiddenRows(),this.initToolbar(),this.initPagination(),this.initBody(),this.initSearchText(),this.initServer()}},{key:"initConstants",value:function(){var t=this.options;this.constants=Qo.CONSTANTS,this.constants.theme=i.default.fn.bootstrapTable.theme,this.constants.dataToggle=this.constants.html.dataToggle||"data-toggle";var e=t.buttonsPrefix?"".concat(t.buttonsPrefix,"-"):"";this.constants.buttonsClass=[t.buttonsPrefix,e+t.buttonsClass,ua.sprintf("".concat(e,"%s"),t.iconSize)].join(" ").trim(),this.buttons=ua.calculateObjectValue(this,t.buttons,[],{}),"object"!==n(this.buttons)&&(this.buttons={}),"string"==typeof t.icons&&(t.icons=ua.calculateObjectValue(null,t.icons))}},{key:"initLocale",value:function(){if(this.options.locale){var e=i.default.fn.bootstrapTable.locales,n=this.options.locale.split(/-|_/);n[0]=n[0].toLowerCase(),n[1]&&(n[1]=n[1].toUpperCase());var o={};e[this.options.locale]?o=e[this.options.locale]:e[n.join("-")]?o=e[n.join("-")]:e[n[0]]&&(o=e[n[0]]);for(var a=0,s=Object.entries(o);a<s.length;a++){var l=r(s[a],2),c=l[0],h=l[1];this.options[c]===t.DEFAULTS[c]&&(this.options[c]=h)}}}},{key:"initContainer",value:function(){var t=["top","both"].includes(this.options.paginationVAlign)?'<div class="fixed-table-pagination clearfix"></div>':"",e=["bottom","both"].includes(this.options.paginationVAlign)?'<div class="fixed-table-pagination"></div>':"",n=ua.calculateObjectValue(this.options,this.options.loadingTemplate,[this.options.formatLoadingMessage()]);this.$container=i.default('\n      <div class="bootstrap-table '.concat(this.constants.theme,'">\n      <div class="fixed-table-toolbar"></div>\n      ').concat(t,'\n      <div class="fixed-table-container">\n      <div class="fixed-table-header"><table></table></div>\n      <div class="fixed-table-body">\n      <div class="fixed-table-loading">\n      ').concat(n,'\n      </div>\n      </div>\n      <div class="fixed-table-footer"></div>\n      </div>\n      ').concat(e,"\n      </div>\n    ")),this.$container.insertAfter(this.$el),this.$tableContainer=this.$container.find(".fixed-table-container"),this.$tableHeader=this.$container.find(".fixed-table-header"),this.$tableBody=this.$container.find(".fixed-table-body"),this.$tableLoading=this.$container.find(".fixed-table-loading"),this.$tableFooter=this.$el.find("tfoot"),this.options.buttonsToolbar?this.$toolbar=i.default("body").find(this.options.buttonsToolbar):this.$toolbar=this.$container.find(".fixed-table-toolbar"),this.$pagination=this.$container.find(".fixed-table-pagination"),this.$tableBody.append(this.$el),this.$container.after('<div class="clearfix"></div>'),this.$el.addClass(this.options.classes),this.$tableLoading.addClass(this.options.classes),this.options.height&&(this.$tableContainer.addClass("fixed-height"),this.options.showFooter&&this.$tableContainer.addClass("has-footer"),this.options.classes.split(" ").includes("table-bordered")&&(this.$tableBody.append('<div class="fixed-table-border"></div>'),this.$tableBorder=this.$tableBody.find(".fixed-table-border"),this.$tableLoading.addClass("fixed-table-border")),this.$tableFooter=this.$container.find(".fixed-table-footer"))}},{key:"initTable",value:function(){var e=this,n=[];if(this.$header=this.$el.find(">thead"),this.$header.length?this.options.theadClasses&&this.$header.addClass(this.options.theadClasses):this.$header=i.default('<thead class="'.concat(this.options.theadClasses,'"></thead>')).appendTo(this.$el),this._headerTrClasses=[],this._headerTrStyles=[],this.$header.find("tr").each((function(t,o){var a=i.default(o),s=[];a.find("th").each((function(t,e){var n=i.default(e);void 0!==n.data("field")&&n.data("field","".concat(n.data("field"))),s.push(i.default.extend({},{title:n.html(),class:n.attr("class"),titleTooltip:n.attr("title"),rowspan:n.attr("rowspan")?+n.attr("rowspan"):void 0,colspan:n.attr("colspan")?+n.attr("colspan"):void 0},n.data()))})),n.push(s),a.attr("class")&&e._headerTrClasses.push(a.attr("class")),a.attr("style")&&e._headerTrStyles.push(a.attr("style"))})),Array.isArray(this.options.columns[0])||(this.options.columns=[this.options.columns]),this.options.columns=i.default.extend(!0,[],n,this.options.columns),this.columns=[],this.fieldsColumnsIndex=[],ua.setFieldIndex(this.options.columns),this.options.columns.forEach((function(n,o){n.forEach((function(n,a){var s=i.default.extend({},t.COLUMN_DEFAULTS,n);void 0!==s.fieldIndex&&(e.columns[s.fieldIndex]=s,e.fieldsColumnsIndex[s.field]=s.fieldIndex),e.options.columns[o][a]=s}))})),!this.options.data.length){var o=ua.trToData(this.columns,this.$el.find(">tbody>tr"));o.length&&(this.options.data=o,this.fromHtml=!0)}this.options.pagination&&"server"!==this.options.sidePagination||(this.footerData=ua.trToData(this.columns,this.$el.find(">tfoot>tr"))),this.footerData&&this.$el.find("tfoot").html("<tr></tr>"),!this.options.showFooter||this.options.cardView?this.$tableFooter.hide():this.$tableFooter.show()}},{key:"initHeader",value:function(){var t=this,e={},n=[];this.header={fields:[],styles:[],classes:[],formatters:[],detailFormatters:[],events:[],sorters:[],sortNames:[],cellStyles:[],searchables:[]},ua.updateFieldGroup(this.options.columns),this.options.columns.forEach((function(i,o){var a=[];a.push("<tr".concat(ua.sprintf(' class="%s"',t._headerTrClasses[o])," ").concat(ua.sprintf(' style="%s"',t._headerTrStyles[o]),">"));var s="";if(0===o&&ua.hasDetailViewIcon(t.options)){var l=t.options.columns.length>1?' rowspan="'.concat(t.options.columns.length,'"'):"";s='<th class="detail"'.concat(l,'>\n          <div class="fht-cell"></div>\n          </th>')}s&&"right"!==t.options.detailViewAlign&&a.push(s),i.forEach((function(i,n){var s=ua.sprintf(' class="%s"',i.class),l=i.widthUnit,c=parseFloat(i.width),h=ua.sprintf("text-align: %s; ",i.halign?i.halign:i.align),u=ua.sprintf("text-align: %s; ",i.align),d=ua.sprintf("vertical-align: %s; ",i.valign);if(d+=ua.sprintf("width: %s; ",!i.checkbox&&!i.radio||c?c?c+l:void 0:i.showSelectTitle?void 0:"36px"),void 0!==i.fieldIndex||i.visible){var f=ua.calculateObjectValue(null,t.options.headerStyle,[i]),p=[],g="";if(f&&f.css)for(var v=0,b=Object.entries(f.css);v<b.length;v++){var m=r(b[v],2),y=m[0],w=m[1];p.push("".concat(y,": ").concat(w))}if(f&&f.classes&&(g=ua.sprintf(' class="%s"',i.class?[i.class,f.classes].join(" "):f.classes)),void 0!==i.fieldIndex){if(t.header.fields[i.fieldIndex]=i.field,t.header.styles[i.fieldIndex]=u+d,t.header.classes[i.fieldIndex]=s,t.header.formatters[i.fieldIndex]=i.formatter,t.header.detailFormatters[i.fieldIndex]=i.detailFormatter,t.header.events[i.fieldIndex]=i.events,t.header.sorters[i.fieldIndex]=i.sorter,t.header.sortNames[i.fieldIndex]=i.sortName,t.header.cellStyles[i.fieldIndex]=i.cellStyle,t.header.searchables[i.fieldIndex]=i.searchable,!i.visible)return;if(t.options.cardView&&!i.cardVisible)return;e[i.field]=i}a.push("<th".concat(ua.sprintf(' title="%s"',i.titleTooltip)),i.checkbox||i.radio?ua.sprintf(' class="bs-checkbox %s"',i.class||""):g||s,ua.sprintf(' style="%s"',h+d+p.join("; ")),ua.sprintf(' rowspan="%s"',i.rowspan),ua.sprintf(' colspan="%s"',i.colspan),ua.sprintf(' data-field="%s"',i.field),0===n&&o>0?" data-not-first-th":"",">"),a.push(ua.sprintf('<div class="th-inner %s">',t.options.sortable&&i.sortable?"sortable both":""));var S=t.options.escape?ua.escapeHTML(i.title):i.title,x=S;i.checkbox&&(S="",!t.options.singleSelect&&t.options.checkboxHeader&&(S='<label><input name="btSelectAll" type="checkbox" /><span></span></label>'),t.header.stateField=i.field),i.radio&&(S="",t.header.stateField=i.field),!S&&i.showSelectTitle&&(S+=x),a.push(S),a.push("</div>"),a.push('<div class="fht-cell"></div>'),a.push("</div>"),a.push("</th>")}})),s&&"right"===t.options.detailViewAlign&&a.push(s),a.push("</tr>"),a.length>3&&n.push(a.join(""))})),this.$header.html(n.join("")),this.$header.find("th[data-field]").each((function(t,n){i.default(n).data(e[i.default(n).data("field")])})),this.$container.off("click",".th-inner").on("click",".th-inner",(function(e){var n=i.default(e.currentTarget);if(t.options.detailView&&!n.parent().hasClass("bs-checkbox")&&n.closest(".bootstrap-table")[0]!==t.$container[0])return!1;t.options.sortable&&n.parent().data().sortable&&t.onSort(e)}));var o=ua.getEventName("resize.bootstrap-table",this.$el.attr("id"));i.default(window).off(o),!this.options.showHeader||this.options.cardView?(this.$header.hide(),this.$tableHeader.hide(),this.$tableLoading.css("top",0)):(this.$header.show(),this.$tableHeader.show(),this.$tableLoading.css("top",this.$header.outerHeight()+1),this.getCaret(),i.default(window).on(o,(function(){return t.resetView()}))),this.$selectAll=this.$header.find('[name="btSelectAll"]'),this.$selectAll.off("click").on("click",(function(e){e.stopPropagation();var n=i.default(e.currentTarget).prop("checked");t[n?"checkAll":"uncheckAll"](),t.updateSelected()}))}},{key:"initData",value:function(t,e){"append"===e?this.options.data=this.options.data.concat(t):"prepend"===e?this.options.data=[].concat(t).concat(this.options.data):(t=t||ua.deepCopy(this.options.data),this.options.data=Array.isArray(t)?t:t[this.options.dataField]),this.data=l(this.options.data),this.options.sortReset&&(this.unsortedData=l(this.data)),"server"!==this.options.sidePagination&&this.initSort()}},{key:"initSort",value:function(){var t=this,e=this.options.sortName,i="desc"===this.options.sortOrder?-1:1,n=this.header.fields.indexOf(this.options.sortName),o=0;-1!==n?(this.options.sortStable&&this.data.forEach((function(t,e){t.hasOwnProperty("_position")||(t._position=e)})),this.options.customSort?ua.calculateObjectValue(this.options,this.options.customSort,[this.options.sortName,this.options.sortOrder,this.data]):this.data.sort((function(o,a){t.header.sortNames[n]&&(e=t.header.sortNames[n]);var s=ua.getItemField(o,e,t.options.escape),r=ua.getItemField(a,e,t.options.escape),l=ua.calculateObjectValue(t.header,t.header.sorters[n],[s,r,o,a]);return void 0!==l?t.options.sortStable&&0===l?i*(o._position-a._position):i*l:ua.sort(s,r,i,t.options.sortStable,o._position,a._position)})),void 0!==this.options.sortClass&&(clearTimeout(o),o=setTimeout((function(){t.$el.removeClass(t.options.sortClass);var e=t.$header.find('[data-field="'.concat(t.options.sortName,'"]')).index();t.$el.find("tr td:nth-child(".concat(e+1,")")).addClass(t.options.sortClass)}),250))):this.options.sortReset&&(this.data=l(this.unsortedData))}},{key:"onSort",value:function(t){var e=t.type,n=t.currentTarget,o="keypress"===e?i.default(n):i.default(n).parent(),a=this.$header.find("th").eq(o.index());if(this.$header.add(this.$header_).find("span.order").remove(),this.options.sortName===o.data("field")){var s=this.options.sortOrder;void 0===s?this.options.sortOrder="asc":"asc"===s?this.options.sortOrder="desc":"desc"===this.options.sortOrder&&(this.options.sortOrder=this.options.sortReset?void 0:"asc"),void 0===this.options.sortOrder&&(this.options.sortName=void 0)}else this.options.sortName=o.data("field"),this.options.rememberOrder?this.options.sortOrder="asc"===o.data("order")?"desc":"asc":this.options.sortOrder=this.columns[this.fieldsColumnsIndex[o.data("field")]].sortOrder||this.columns[this.fieldsColumnsIndex[o.data("field")]].order;if(this.trigger("sort",this.options.sortName,this.options.sortOrder),o.add(a).data("order",this.options.sortOrder),this.getCaret(),"server"===this.options.sidePagination&&this.options.serverSort)return this.options.pageNumber=1,void this.initServer(this.options.silentSort);this.initSort(),this.initBody()}},{key:"initToolbar",value:function(){var t,e=this,o=this.options,a=[],s=0,l=0;this.$toolbar.find(".bs-bars").children().length&&i.default("body").append(i.default(o.toolbar)),this.$toolbar.html(""),"string"!=typeof o.toolbar&&"object"!==n(o.toolbar)||i.default(ua.sprintf('<div class="bs-bars %s-%s"></div>',this.constants.classes.pull,o.toolbarAlign)).appendTo(this.$toolbar).append(i.default(o.toolbar)),a=['<div class="'.concat(["columns","columns-".concat(o.buttonsAlign),this.constants.classes.buttonsGroup,"".concat(this.constants.classes.pull,"-").concat(o.buttonsAlign)].join(" "),'">')],"string"==typeof o.buttonsOrder&&(o.buttonsOrder=o.buttonsOrder.replace(/\[|\]| |'/g,"").split(",")),this.buttons=Object.assign(this.buttons,{paginationSwitch:{text:o.pagination?o.formatPaginationSwitchUp():o.formatPaginationSwitchDown(),icon:o.pagination?o.icons.paginationSwitchDown:o.icons.paginationSwitchUp,render:!1,event:this.togglePagination,attributes:{"aria-label":o.formatPaginationSwitch(),title:o.formatPaginationSwitch()}},refresh:{text:o.formatRefresh(),icon:o.icons.refresh,render:!1,event:this.refresh,attributes:{"aria-label":o.formatRefresh(),title:o.formatRefresh()}},toggle:{text:o.formatToggle(),icon:o.icons.toggleOff,render:!1,event:this.toggleView,attributes:{"aria-label":o.formatToggleOn(),title:o.formatToggleOn()}},fullscreen:{text:o.formatFullscreen(),icon:o.icons.fullscreen,render:!1,event:this.toggleFullscreen,attributes:{"aria-label":o.formatFullscreen(),title:o.formatFullscreen()}},columns:{render:!1,html:function(){var t=[];if(t.push('<div class="keep-open '.concat(e.constants.classes.buttonsDropdown,'" title="').concat(o.formatColumns(),'">\n            <button class="').concat(e.constants.buttonsClass,' dropdown-toggle" type="button" ').concat(e.constants.dataToggle,'="dropdown"\n            aria-label="Columns" title="').concat(o.formatColumns(),'">\n            ').concat(o.showButtonIcons?ua.sprintf(e.constants.html.icon,o.iconsPrefix,o.icons.columns):"","\n            ").concat(o.showButtonText?o.formatColumns():"","\n            ").concat(e.constants.html.dropdownCaret,"\n            </button>\n            ").concat(e.constants.html.toolbarDropdown[0])),o.showColumnsSearch&&(t.push(ua.sprintf(e.constants.html.toolbarDropdownItem,ua.sprintf('<input type="text" class="%s" name="columnsSearch" placeholder="%s" autocomplete="off">',e.constants.classes.input,o.formatSearch()))),t.push(e.constants.html.toolbarDropdownSeparator)),o.showColumnsToggleAll){var i=e.getVisibleColumns().length===e.columns.filter((function(t){return!e.isSelectionColumn(t)})).length;t.push(ua.sprintf(e.constants.html.toolbarDropdownItem,ua.sprintf('<input type="checkbox" class="toggle-all" %s> <span>%s</span>',i?'checked="checked"':"",o.formatColumnsToggleAll()))),t.push(e.constants.html.toolbarDropdownSeparator)}var n=0;return e.columns.forEach((function(t){t.visible&&n++})),e.columns.forEach((function(i,a){if(!e.isSelectionColumn(i)&&(!o.cardView||i.cardVisible)){var s=i.visible?' checked="checked"':"",r=n<=o.minimumCountColumns&&s?' disabled="disabled"':"";i.switchable&&(t.push(ua.sprintf(e.constants.html.toolbarDropdownItem,ua.sprintf('<input type="checkbox" data-field="%s" value="%s"%s%s> <span>%s</span>',i.field,a,s,r,i.title))),l++)}})),t.push(e.constants.html.toolbarDropdown[1],"</div>"),t.join("")}}});for(var c={},h=0,d=Object.entries(this.buttons);h<d.length;h++){var f=r(d[h],2),p=f[0],g=f[1],v=void 0;if(g.hasOwnProperty("html"))"function"==typeof g.html?v=g.html():"string"==typeof g.html&&(v=g.html);else{if(v='<button class="'.concat(this.constants.buttonsClass,'" type="button" name="').concat(p,'"'),g.hasOwnProperty("attributes"))for(var b=0,m=Object.entries(g.attributes);b<m.length;b++){var y=r(m[b],2),w=y[0],S=y[1];v+=" ".concat(w,'="').concat(S,'"')}v+=">",o.showButtonIcons&&g.hasOwnProperty("icon")&&(v+="".concat(ua.sprintf(this.constants.html.icon,o.iconsPrefix,g.icon)," ")),o.showButtonText&&g.hasOwnProperty("text")&&(v+=g.text),v+="</button>"}c[p]=v;var x="show".concat(p.charAt(0).toUpperCase()).concat(p.substring(1)),k=o[x];!(!g.hasOwnProperty("render")||g.hasOwnProperty("render")&&g.render)||void 0!==k&&!0!==k||(o[x]=!0),o.buttonsOrder.includes(p)||o.buttonsOrder.push(p)}var O,C=u(o.buttonsOrder);try{for(C.s();!(O=C.n()).done;){var T=O.value;o["show".concat(T.charAt(0).toUpperCase()).concat(T.substring(1))]&&a.push(c[T])}}catch(t){C.e(t)}finally{C.f()}a.push("</div>"),(this.showToolbar||a.length>2)&&this.$toolbar.append(a.join(""));for(var P=0,I=Object.entries(this.buttons);P<I.length;P++){var A=r(I[P],2),$=A[0],E=A[1];if(E.hasOwnProperty("event")){if("function"==typeof E.event||"string"==typeof E.event)if("continue"===function(){var t="string"==typeof E.event?window[E.event]:E.event;return e.$toolbar.find('button[name="'.concat($,'"]')).off("click").on("click",(function(){return t.call(e)})),"continue"}())continue;for(var R=function(){var t=r(_[j],2),i=t[0],n=t[1],o="string"==typeof n?window[n]:n;e.$toolbar.find('button[name="'.concat($,'"]')).off(i).on(i,(function(){return o.call(e)}))},j=0,_=Object.entries(E.event);j<_.length;j++)R()}}if(o.showColumns){var N=(t=this.$toolbar.find(".keep-open")).find('input[type="checkbox"]:not(".toggle-all")'),F=t.find('input[type="checkbox"].toggle-all');if(l<=o.minimumCountColumns&&t.find("input").prop("disabled",!0),t.find("li, label").off("click").on("click",(function(t){t.stopImmediatePropagation()})),N.off("click").on("click",(function(t){var n=t.currentTarget,o=i.default(n);e._toggleColumn(o.val(),o.prop("checked"),!1),e.trigger("column-switch",o.data("field"),o.prop("checked")),F.prop("checked",N.filter(":checked").length===e.columns.filter((function(t){return!e.isSelectionColumn(t)})).length)})),F.off("click").on("click",(function(t){var n=t.currentTarget;e._toggleAllColumns(i.default(n).prop("checked"))})),o.showColumnsSearch){var D=t.find('[name="columnsSearch"]'),V=t.find(".dropdown-item-marker");D.on("keyup paste change",(function(t){var e=t.currentTarget,n=i.default(e).val().toLowerCase();V.show(),N.each((function(t,e){var o=i.default(e).parents(".dropdown-item-marker");o.text().toLowerCase().includes(n)||o.hide()}))}))}}var B=function(t){var i="keyup drop blur mouseup";t.off(i).on(i,(function(t){o.searchOnEnterKey&&13!==t.keyCode||[37,38,39,40].includes(t.keyCode)||(clearTimeout(s),s=setTimeout((function(){e.onSearch({currentTarget:t.currentTarget})}),o.searchTimeOut))}))};if((o.search||this.showSearchClearButton)&&"string"!=typeof o.searchSelector){a=[];var L=ua.sprintf(this.constants.html.searchButton,this.constants.buttonsClass,o.formatSearch(),o.showButtonIcons?ua.sprintf(this.constants.html.icon,o.iconsPrefix,o.icons.search):"",o.showButtonText?o.formatSearch():""),H=ua.sprintf(this.constants.html.searchClearButton,this.constants.buttonsClass,o.formatClearSearch(),o.showButtonIcons?ua.sprintf(this.constants.html.icon,o.iconsPrefix,o.icons.clearSearch):"",o.showButtonText?o.formatClearSearch():""),M='<input class="'.concat(this.constants.classes.input,"\n        ").concat(ua.sprintf(" %s%s",this.constants.classes.inputPrefix,o.iconSize),'\n        search-input" type="search" placeholder="').concat(o.formatSearch(),'" autocomplete="off">'),U=M;if(o.showSearchButton||o.showSearchClearButton){var z=(o.showSearchButton?L:"")+(o.showSearchClearButton?H:"");U=o.search?ua.sprintf(this.constants.html.inputGroup,M,z):z}a.push(ua.sprintf('\n        <div class="'.concat(this.constants.classes.pull,"-").concat(o.searchAlign," search ").concat(this.constants.classes.inputGroup,'">\n          %s\n        </div>\n      '),U)),this.$toolbar.append(a.join(""));var q=ua.getSearchInput(this);o.showSearchButton?(this.$toolbar.find(".search button[name=search]").off("click").on("click",(function(){clearTimeout(s),s=setTimeout((function(){e.onSearch({currentTarget:q})}),o.searchTimeOut)})),o.searchOnEnterKey&&B(q)):B(q),o.showSearchClearButton&&this.$toolbar.find(".search button[name=clearSearch]").click((function(){e.resetSearch()}))}else if("string"==typeof o.searchSelector){B(ua.getSearchInput(this))}}},{key:"onSearch",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.currentTarget,n=t.firedByInitSearchText,o=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(void 0!==e&&i.default(e).length&&o){var a=i.default(e).val().trim();if(this.options.trimOnSearch&&i.default(e).val()!==a&&i.default(e).val(a),this.searchText===a)return;var s=ua.getSearchInput(this),r=e instanceof jQuery?e:i.default(e);(r.is(s)||r.hasClass("search-input"))&&(this.searchText=a,this.options.searchText=a)}n||(this.options.pageNumber=1),this.initSearch(),n?"client"===this.options.sidePagination&&this.updatePagination():this.updatePagination(),this.trigger("search",this.searchText)}},{key:"initSearch",value:function(){var t=this;if(this.filterOptions=this.filterOptions||this.options.filterOptions,"server"!==this.options.sidePagination){if(this.options.customSearch)return this.data=ua.calculateObjectValue(this.options,this.options.customSearch,[this.options.data,this.searchText,this.filterColumns]),void(this.options.sortReset&&(this.unsortedData=l(this.data)));var e=this.searchText&&(this.fromHtml?ua.escapeHTML(this.searchText):this.searchText),i=e?e.toLowerCase():"",n=ua.isEmptyObject(this.filterColumns)?null:this.filterColumns;this.options.searchAccentNeutralise&&(i=ua.normalizeAccent(i)),"function"==typeof this.filterOptions.filterAlgorithm?this.data=this.options.data.filter((function(e){return t.filterOptions.filterAlgorithm.apply(null,[e,n])})):"string"==typeof this.filterOptions.filterAlgorithm&&(this.data=n?this.options.data.filter((function(e){var i=t.filterOptions.filterAlgorithm;if("and"===i){for(var o in n)if(Array.isArray(n[o])&&!n[o].includes(e[o])||!Array.isArray(n[o])&&e[o]!==n[o])return!1}else if("or"===i){var a=!1;for(var s in n)(Array.isArray(n[s])&&n[s].includes(e[s])||!Array.isArray(n[s])&&e[s]===n[s])&&(a=!0);return a}return!0})):l(this.options.data));var o=this.getVisibleFields();this.data=i?this.data.filter((function(n,a){for(var s=0;s<t.header.fields.length;s++)if(t.header.searchables[s]&&(!t.options.visibleSearch||-1!==o.indexOf(t.header.fields[s]))){var r=ua.isNumeric(t.header.fields[s])?parseInt(t.header.fields[s],10):t.header.fields[s],l=t.columns[t.fieldsColumnsIndex[r]],c=void 0;if("string"==typeof r){c=n;for(var h=r.split("."),u=0;u<h.length;u++)null!==c[h[u]]&&(c=c[h[u]])}else c=n[r];if(t.options.searchAccentNeutralise&&(c=ua.normalizeAccent(c)),l&&l.searchFormatter&&(c=ua.calculateObjectValue(l,t.header.formatters[s],[c,n,a,l.field],c)),"string"==typeof c||"number"==typeof c){if(t.options.strictSearch&&"".concat(c).toLowerCase()===i||t.options.regexSearch&&ua.regexCompare(c,e))return!0;var d=/(?:(<=|=>|=<|>=|>|<)(?:\s+)?(-?\d+)?|(-?\d+)?(\s+)?(<=|=>|=<|>=|>|<))/gm.exec(t.searchText),f=!1;if(d){var p=d[1]||"".concat(d[5],"l"),g=d[2]||d[3],v=parseInt(c,10),b=parseInt(g,10);switch(p){case">":case"<l":f=v>b;break;case"<":case">l":f=v<b;break;case"<=":case"=<":case">=l":case"=>l":f=v<=b;break;case">=":case"=>":case"<=l":case"=<l":f=v>=b}}if(f||"".concat(c).toLowerCase().includes(i))return!0}}return!1})):this.data,this.options.sortReset&&(this.unsortedData=l(this.data)),this.initSort()}}},{key:"initPagination",value:function(){var t=this,e=this.options;if(e.pagination){this.$pagination.show();var i,n,o,a,s,r,l,c=[],h=!1,u=this.getData({includeHiddenRows:!1}),d=e.pageList;if("string"==typeof d&&(d=d.replace(/\[|\]| /g,"").toLowerCase().split(",")),d=d.map((function(t){return"string"==typeof t?t.toLowerCase()===e.formatAllRows().toLowerCase()||["all","unlimited"].includes(t.toLowerCase())?e.formatAllRows():+t:t})),this.paginationParts=e.paginationParts,"string"==typeof this.paginationParts&&(this.paginationParts=this.paginationParts.replace(/\[|\]| |'/g,"").split(",")),"server"!==e.sidePagination&&(e.totalRows=u.length),this.totalPages=0,e.totalRows&&(e.pageSize===e.formatAllRows()&&(e.pageSize=e.totalRows,h=!0),this.totalPages=1+~~((e.totalRows-1)/e.pageSize),e.totalPages=this.totalPages),this.totalPages>0&&e.pageNumber>this.totalPages&&(e.pageNumber=this.totalPages),this.pageFrom=(e.pageNumber-1)*e.pageSize+1,this.pageTo=e.pageNumber*e.pageSize,this.pageTo>e.totalRows&&(this.pageTo=e.totalRows),this.options.pagination&&"server"!==this.options.sidePagination&&(this.options.totalNotFiltered=this.options.data.length),this.options.showExtendedPagination||(this.options.totalNotFiltered=void 0),(this.paginationParts.includes("pageInfo")||this.paginationParts.includes("pageInfoShort")||this.paginationParts.includes("pageSize"))&&c.push('<div class="'.concat(this.constants.classes.pull,"-").concat(e.paginationDetailHAlign,' pagination-detail">')),this.paginationParts.includes("pageInfo")||this.paginationParts.includes("pageInfoShort")){var f=this.paginationParts.includes("pageInfoShort")?e.formatDetailPagination(e.totalRows):e.formatShowingRows(this.pageFrom,this.pageTo,e.totalRows,e.totalNotFiltered);c.push('<span class="pagination-info">\n      '.concat(f,"\n      </span>"))}if(this.paginationParts.includes("pageSize")){c.push('<div class="page-list">');var p=['<div class="'.concat(this.constants.classes.paginationDropdown,'">\n        <button class="').concat(this.constants.buttonsClass,' dropdown-toggle" type="button" ').concat(this.constants.dataToggle,'="dropdown">\n        <span class="page-size">\n        ').concat(h?e.formatAllRows():e.pageSize,"\n        </span>\n        ").concat(this.constants.html.dropdownCaret,"\n        </button>\n        ").concat(this.constants.html.pageDropdown[0])];d.forEach((function(i,n){var o;(!e.smartDisplay||0===n||d[n-1]<e.totalRows||i===e.formatAllRows())&&(o=h?i===e.formatAllRows()?t.constants.classes.dropdownActive:"":i===e.pageSize?t.constants.classes.dropdownActive:"",p.push(ua.sprintf(t.constants.html.pageDropdownItem,o,i)))})),p.push("".concat(this.constants.html.pageDropdown[1],"</div>")),c.push(e.formatRecordsPerPage(p.join("")))}if((this.paginationParts.includes("pageInfo")||this.paginationParts.includes("pageInfoShort")||this.paginationParts.includes("pageSize"))&&c.push("</div></div>"),this.paginationParts.includes("pageList")){c.push('<div class="'.concat(this.constants.classes.pull,"-").concat(e.paginationHAlign,' pagination">'),ua.sprintf(this.constants.html.pagination[0],ua.sprintf(" pagination-%s",e.iconSize)),ua.sprintf(this.constants.html.paginationItem," page-pre",e.formatSRPaginationPreText(),e.paginationPreText)),this.totalPages<e.paginationSuccessivelySize?(n=1,o=this.totalPages):o=(n=e.pageNumber-e.paginationPagesBySide)+2*e.paginationPagesBySide,e.pageNumber<e.paginationSuccessivelySize-1&&(o=e.paginationSuccessivelySize),e.paginationSuccessivelySize>this.totalPages-n&&(n=n-(e.paginationSuccessivelySize-(this.totalPages-n))+1),n<1&&(n=1),o>this.totalPages&&(o=this.totalPages);var g=Math.round(e.paginationPagesBySide/2),v=function(i){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return ua.sprintf(t.constants.html.paginationItem,n+(i===e.pageNumber?" ".concat(t.constants.classes.paginationActive):""),e.formatSRPaginationPageText(i),i)};if(n>1){var b=e.paginationPagesBySide;for(b>=n&&(b=n-1),i=1;i<=b;i++)c.push(v(i));n-1===b+1?(i=n-1,c.push(v(i))):n-1>b&&(n-2*e.paginationPagesBySide>e.paginationPagesBySide&&e.paginationUseIntermediate?(i=Math.round((n-g)/2+g),c.push(v(i," page-intermediate"))):c.push(ua.sprintf(this.constants.html.paginationItem," page-first-separator disabled","","...")))}for(i=n;i<=o;i++)c.push(v(i));if(this.totalPages>o){var m=this.totalPages-(e.paginationPagesBySide-1);for(o>=m&&(m=o+1),o+1===m-1?(i=o+1,c.push(v(i))):m>o+1&&(this.totalPages-o>2*e.paginationPagesBySide&&e.paginationUseIntermediate?(i=Math.round((this.totalPages-g-o)/2+o),c.push(v(i," page-intermediate"))):c.push(ua.sprintf(this.constants.html.paginationItem," page-last-separator disabled","","..."))),i=m;i<=this.totalPages;i++)c.push(v(i))}c.push(ua.sprintf(this.constants.html.paginationItem," page-next",e.formatSRPaginationNextText(),e.paginationNextText)),c.push(this.constants.html.pagination[1],"</div>")}this.$pagination.html(c.join(""));var y=["bottom","both"].includes(e.paginationVAlign)?" ".concat(this.constants.classes.dropup):"";this.$pagination.last().find(".page-list > div").addClass(y),e.onlyInfoPagination||(a=this.$pagination.find(".page-list a"),s=this.$pagination.find(".page-pre"),r=this.$pagination.find(".page-next"),l=this.$pagination.find(".page-item").not(".page-next, .page-pre, .page-last-separator, .page-first-separator"),this.totalPages<=1&&this.$pagination.find("div.pagination").hide(),e.smartDisplay&&(d.length<2||e.totalRows<=d[0])&&this.$pagination.find("div.page-list").hide(),this.$pagination[this.getData().length?"show":"hide"](),e.paginationLoop||(1===e.pageNumber&&s.addClass("disabled"),e.pageNumber===this.totalPages&&r.addClass("disabled")),h&&(e.pageSize=e.formatAllRows()),a.off("click").on("click",(function(e){return t.onPageListChange(e)})),s.off("click").on("click",(function(e){return t.onPagePre(e)})),r.off("click").on("click",(function(e){return t.onPageNext(e)})),l.off("click").on("click",(function(e){return t.onPageNumber(e)})))}else this.$pagination.hide()}},{key:"updatePagination",value:function(t){t&&i.default(t.currentTarget).hasClass("disabled")||(this.options.maintainMetaData||this.resetRows(),this.initPagination(),this.trigger("page-change",this.options.pageNumber,this.options.pageSize),"server"===this.options.sidePagination?this.initServer():this.initBody())}},{key:"onPageListChange",value:function(t){t.preventDefault();var e=i.default(t.currentTarget);return e.parent().addClass(this.constants.classes.dropdownActive).siblings().removeClass(this.constants.classes.dropdownActive),this.options.pageSize=e.text().toUpperCase()===this.options.formatAllRows().toUpperCase()?this.options.formatAllRows():+e.text(),this.$toolbar.find(".page-size").text(this.options.pageSize),this.updatePagination(t),!1}},{key:"onPagePre",value:function(t){if(!i.default(t.target).hasClass("disabled"))return t.preventDefault(),this.options.pageNumber-1==0?this.options.pageNumber=this.options.totalPages:this.options.pageNumber--,this.updatePagination(t),!1}},{key:"onPageNext",value:function(t){if(!i.default(t.target).hasClass("disabled"))return t.preventDefault(),this.options.pageNumber+1>this.options.totalPages?this.options.pageNumber=1:this.options.pageNumber++,this.updatePagination(t),!1}},{key:"onPageNumber",value:function(t){if(t.preventDefault(),this.options.pageNumber!==+i.default(t.currentTarget).text())return this.options.pageNumber=+i.default(t.currentTarget).text(),this.updatePagination(t),!1}},{key:"initRow",value:function(t,e,i,o){var a=this,s=[],l={},c=[],h="",u={},d=[];if(!(ua.findIndex(this.hiddenRows,t)>-1)){if((l=ua.calculateObjectValue(this.options,this.options.rowStyle,[t,e],l))&&l.css)for(var f=0,p=Object.entries(l.css);f<p.length;f++){var g=r(p[f],2),v=g[0],b=g[1];c.push("".concat(v,": ").concat(b))}if(u=ua.calculateObjectValue(this.options,this.options.rowAttributes,[t,e],u))for(var m=0,y=Object.entries(u);m<y.length;m++){var w=r(y[m],2),S=w[0],x=w[1];d.push("".concat(S,'="').concat(ua.escapeHTML(x),'"'))}if(t._data&&!ua.isEmptyObject(t._data))for(var k=0,O=Object.entries(t._data);k<O.length;k++){var C=r(O[k],2),T=C[0],P=C[1];if("index"===T)return;h+=" data-".concat(T,"='").concat("object"===n(P)?JSON.stringify(P):P,"'")}s.push("<tr",ua.sprintf(" %s",d.length?d.join(" "):void 0),ua.sprintf(' id="%s"',Array.isArray(t)?void 0:t._id),ua.sprintf(' class="%s"',l.classes||(Array.isArray(t)?void 0:t._class)),ua.sprintf(' style="%s"',Array.isArray(t)?void 0:t._style),' data-index="'.concat(e,'"'),ua.sprintf(' data-uniqueid="%s"',ua.getItemField(t,this.options.uniqueId,!1)),ua.sprintf(' data-has-detail-view="%s"',this.options.detailView&&ua.calculateObjectValue(null,this.options.detailFilter,[e,t])?"true":void 0),ua.sprintf("%s",h),">"),this.options.cardView&&s.push('<td colspan="'.concat(this.header.fields.length,'"><div class="card-views">'));var I="";return ua.hasDetailViewIcon(this.options)&&(I="<td>",ua.calculateObjectValue(null,this.options.detailFilter,[e,t])&&(I+='\n          <a class="detail-icon" href="#">\n          '.concat(ua.sprintf(this.constants.html.icon,this.options.iconsPrefix,this.options.icons.detailOpen),"\n          </a>\n        ")),I+="</td>"),I&&"right"!==this.options.detailViewAlign&&s.push(I),this.header.fields.forEach((function(i,n){var o="",l=ua.getItemField(t,i,a.options.escape),h="",u="",d={},f="",p=a.header.classes[n],g="",v="",b="",m="",y="",w="",S=a.columns[n];if((!a.fromHtml&&!a.autoMergeCells||void 0!==l||S.checkbox||S.radio)&&S.visible&&(!a.options.cardView||S.cardVisible)){if(S.escape&&(l=ua.escapeHTML(l)),c.concat([a.header.styles[n]]).length&&(v+="".concat(c.concat([a.header.styles[n]]).join("; "))),t["_".concat(i,"_style")]&&(v+="".concat(t["_".concat(i,"_style")])),v&&(g=' style="'.concat(v,'"')),t["_".concat(i,"_id")]&&(f=ua.sprintf(' id="%s"',t["_".concat(i,"_id")])),t["_".concat(i,"_class")]&&(p=ua.sprintf(' class="%s"',t["_".concat(i,"_class")])),t["_".concat(i,"_rowspan")]&&(m=ua.sprintf(' rowspan="%s"',t["_".concat(i,"_rowspan")])),t["_".concat(i,"_colspan")]&&(y=ua.sprintf(' colspan="%s"',t["_".concat(i,"_colspan")])),t["_".concat(i,"_title")]&&(w=ua.sprintf(' title="%s"',t["_".concat(i,"_title")])),(d=ua.calculateObjectValue(a.header,a.header.cellStyles[n],[l,t,e,i],d)).classes&&(p=' class="'.concat(d.classes,'"')),d.css){for(var x=[],k=0,O=Object.entries(d.css);k<O.length;k++){var C=r(O[k],2),T=C[0],P=C[1];x.push("".concat(T,": ").concat(P))}g=' style="'.concat(x.concat(a.header.styles[n]).join("; "),'"')}if(h=ua.calculateObjectValue(S,a.header.formatters[n],[l,t,e,i],l),S.checkbox||S.radio||(h=null==h?a.options.undefinedText:h),S.searchable&&a.searchText&&a.options.searchHighlight&&!S.checkbox&&!S.radio){var I="",A=new RegExp("(".concat(a.searchText.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),")"),"gim"),$="<mark>$1</mark>";if(h&&/<(?=.*? .*?\/ ?>|br|hr|input|!--|wbr)[a-z]+.*?>|<([a-z]+).*?<\/\1>/i.test(h)){var E=(new DOMParser).parseFromString(h.toString(),"text/html").documentElement.textContent,R=E.replace(A,$);E=E.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),I=h.replace(new RegExp("(>\\s*)(".concat(E,")(\\s*)"),"gm"),"$1".concat(R,"$3"))}else I=h.toString().replace(A,$);h=ua.calculateObjectValue(S,S.searchHighlightFormatter,[h,a.searchText],I)}if(t["_".concat(i,"_data")]&&!ua.isEmptyObject(t["_".concat(i,"_data")]))for(var j=0,_=Object.entries(t["_".concat(i,"_data")]);j<_.length;j++){var N=r(_[j],2),F=N[0],D=N[1];if("index"===F)return;b+=" data-".concat(F,'="').concat(D,'"')}if(S.checkbox||S.radio){u=S.checkbox?"checkbox":u,u=S.radio?"radio":u;var V=S.class||"",B=ua.isObject(h)&&h.hasOwnProperty("checked")?h.checked:(!0===h||l)&&!1!==h,L=!S.checkboxEnabled||h&&h.disabled;o=[a.options.cardView?'<div class="card-view '.concat(V,'">'):'<td class="bs-checkbox '.concat(V,'"').concat(p).concat(g,">"),'<label>\n            <input\n            data-index="'.concat(e,'"\n            name="').concat(a.options.selectItemName,'"\n            type="').concat(u,'"\n            ').concat(ua.sprintf('value="%s"',t[a.options.idField]),"\n            ").concat(ua.sprintf('checked="%s"',B?"checked":void 0),"\n            ").concat(ua.sprintf('disabled="%s"',L?"disabled":void 0)," />\n            <span></span>\n            </label>"),a.header.formatters[n]&&"string"==typeof h?h:"",a.options.cardView?"</div>":"</td>"].join(""),t[a.header.stateField]=!0===h||!!l||h&&h.checked}else if(a.options.cardView){var H=a.options.showHeader?'<span class="card-view-title '.concat(d.classes,'"').concat(g,">").concat(ua.getFieldTitle(a.columns,i),"</span>"):"";o='<div class="card-view">'.concat(H,'<span class="card-view-value ').concat(d.classes,'"').concat(g,">").concat(h,"</span></div>"),a.options.smartDisplay&&""===h&&(o='<div class="card-view"></div>')}else o="<td".concat(f).concat(p).concat(g).concat(b).concat(m).concat(y).concat(w,">").concat(h,"</td>");s.push(o)}})),I&&"right"===this.options.detailViewAlign&&s.push(I),this.options.cardView&&s.push("</div></td>"),s.push("</tr>"),s.join("")}}},{key:"initBody",value:function(t,e){var n=this,o=this.getData();this.trigger("pre-body",o),this.$body=this.$el.find(">tbody"),this.$body.length||(this.$body=i.default("<tbody></tbody>").appendTo(this.$el)),this.options.pagination&&"server"!==this.options.sidePagination||(this.pageFrom=1,this.pageTo=o.length);var a=[],s=i.default(document.createDocumentFragment()),r=!1,l=[];this.autoMergeCells=ua.checkAutoMergeCells(o.slice(this.pageFrom-1,this.pageTo));for(var c=this.pageFrom-1;c<this.pageTo;c++){var h=o[c],u=this.initRow(h,c,o,s);if(r=r||!!u,u&&"string"==typeof u){var d=this.options.uniqueId;if(d&&h.hasOwnProperty(d)){var f=h[d],p=this.$body.find(ua.sprintf('> tr[data-uniqueid="%s"][data-has-detail-view]',f)).next();p.is("tr.detail-view")&&(l.push(c),e&&f===e||(u+=p[0].outerHTML))}this.options.virtualScroll?a.push(u):s.append(u)}}r?this.options.virtualScroll?(this.virtualScroll&&this.virtualScroll.destroy(),this.virtualScroll=new da({rows:a,fixedScroll:t,scrollEl:this.$tableBody[0],contentEl:this.$body[0],itemHeight:this.options.virtualScrollItemHeight,callback:function(t,e){n.fitHeader(),n.initBodyEvent(),n.trigger("virtual-scroll",t,e)}})):this.$body.html(s):this.$body.html('<tr class="no-records-found">'.concat(ua.sprintf('<td colspan="%s">%s</td>',this.getVisibleFields().length+ua.getDetailViewIndexOffset(this.options),this.options.formatNoMatches()),"</tr>")),l.forEach((function(t){n.expandRow(t)})),t||this.scrollTo(0),this.initBodyEvent(),this.initFooter(),this.resetView(),this.updateSelected(),"server"!==this.options.sidePagination&&(this.options.totalRows=o.length),this.trigger("post-body",o)}},{key:"initBodyEvent",value:function(){var t=this;this.$body.find("> tr[data-index] > td").off("click dblclick").on("click dblclick",(function(e){var n=i.default(e.currentTarget),o=n.parent(),a=i.default(e.target).parents(".card-views").children(),s=i.default(e.target).parents(".card-view"),r=o.data("index"),l=t.data[r],c=t.options.cardView?a.index(s):n[0].cellIndex,h=t.getVisibleFields()[c-ua.getDetailViewIndexOffset(t.options)],u=t.columns[t.fieldsColumnsIndex[h]],d=ua.getItemField(l,h,t.options.escape);if(!n.find(".detail-icon").length){if(t.trigger("click"===e.type?"click-cell":"dbl-click-cell",h,d,l,n),t.trigger("click"===e.type?"click-row":"dbl-click-row",l,o,h),"click"===e.type&&t.options.clickToSelect&&u.clickToSelect&&!ua.calculateObjectValue(t.options,t.options.ignoreClickToSelectOn,[e.target])){var f=o.find(ua.sprintf('[name="%s"]',t.options.selectItemName));f.length&&f[0].click()}"click"===e.type&&t.options.detailViewByClick&&t.toggleDetailView(r,t.header.detailFormatters[t.fieldsColumnsIndex[h]])}})).off("mousedown").on("mousedown",(function(e){t.multipleSelectRowCtrlKey=e.ctrlKey||e.metaKey,t.multipleSelectRowShiftKey=e.shiftKey})),this.$body.find("> tr[data-index] > td > .detail-icon").off("click").on("click",(function(e){return e.preventDefault(),t.toggleDetailView(i.default(e.currentTarget).parent().parent().data("index")),!1})),this.$selectItem=this.$body.find(ua.sprintf('[name="%s"]',this.options.selectItemName)),this.$selectItem.off("click").on("click",(function(e){e.stopImmediatePropagation();var n=i.default(e.currentTarget);t._toggleCheck(n.prop("checked"),n.data("index"))})),this.header.events.forEach((function(e,n){var o=e;if(o){"string"==typeof o&&(o=ua.calculateObjectValue(null,o));var a=t.header.fields[n],s=t.getVisibleFields().indexOf(a);if(-1!==s){s+=ua.getDetailViewIndexOffset(t.options);var r=function(e){if(!o.hasOwnProperty(e))return"continue";var n=o[e];t.$body.find(">tr:not(.no-records-found)").each((function(o,r){var l=i.default(r),c=l.find(t.options.cardView?".card-views>.card-view":">td").eq(s),h=e.indexOf(" "),u=e.substring(0,h),d=e.substring(h+1);c.find(d).off(u).on(u,(function(e){var i=l.data("index"),o=t.data[i],s=o[a];n.apply(t,[e,s,o,i])}))}))};for(var l in o)r(l)}}}))}},{key:"initServer",value:function(t,e,n){var o=this,a={},s=this.header.fields.indexOf(this.options.sortName),r={searchText:this.searchText,sortName:this.options.sortName,sortOrder:this.options.sortOrder};if(this.header.sortNames[s]&&(r.sortName=this.header.sortNames[s]),this.options.pagination&&"server"===this.options.sidePagination&&(r.pageSize=this.options.pageSize===this.options.formatAllRows()?this.options.totalRows:this.options.pageSize,r.pageNumber=this.options.pageNumber),n||this.options.url||this.options.ajax){if("limit"===this.options.queryParamsType&&(r={search:r.searchText,sort:r.sortName,order:r.sortOrder},this.options.pagination&&"server"===this.options.sidePagination&&(r.offset=this.options.pageSize===this.options.formatAllRows()?0:this.options.pageSize*(this.options.pageNumber-1),r.limit=this.options.pageSize,0!==r.limit&&this.options.pageSize!==this.options.formatAllRows()||delete r.limit)),this.options.search&&"server"===this.options.sidePagination&&this.columns.filter((function(t){return!t.searchable})).length){r.searchable=[];var l,c=u(this.columns);try{for(c.s();!(l=c.n()).done;){var h=l.value;!h.checkbox&&h.searchable&&(this.options.visibleSearch&&h.visible||!this.options.visibleSearch)&&r.searchable.push(h.field)}}catch(t){c.e(t)}finally{c.f()}}if(ua.isEmptyObject(this.filterColumnsPartial)||(r.filter=JSON.stringify(this.filterColumnsPartial,null)),i.default.extend(r,e||{}),!1!==(a=ua.calculateObjectValue(this.options,this.options.queryParams,[r],a))){t||this.showLoading();var d=i.default.extend({},ua.calculateObjectValue(null,this.options.ajaxOptions),{type:this.options.method,url:n||this.options.url,data:"application/json"===this.options.contentType&&"post"===this.options.method?JSON.stringify(a):a,cache:this.options.cache,contentType:this.options.contentType,dataType:this.options.dataType,success:function(e,i,n){var a=ua.calculateObjectValue(o.options,o.options.responseHandler,[e,n],e);o.load(a),o.trigger("load-success",a,n&&n.status,n),t||o.hideLoading(),"server"===o.options.sidePagination&&o.options.pageNumber>1&&a[o.options.totalField]>0&&!a[o.options.dataField].length&&o.updatePagination()},error:function(e){if(e&&0===e.status&&o._xhrAbort)o._xhrAbort=!1;else{var i=[];"server"===o.options.sidePagination&&((i={})[o.options.totalField]=0,i[o.options.dataField]=[]),o.load(i),o.trigger("load-error",e&&e.status,e),t||o.$tableLoading.hide()}}});return this.options.ajax?ua.calculateObjectValue(this,this.options.ajax,[d],null):(this._xhr&&4!==this._xhr.readyState&&(this._xhrAbort=!0,this._xhr.abort()),this._xhr=i.default.ajax(d)),a}}}},{key:"initSearchText",value:function(){if(this.options.search&&(this.searchText="",""!==this.options.searchText)){var t=ua.getSearchInput(this);t.val(this.options.searchText),this.onSearch({currentTarget:t,firedByInitSearchText:!0})}}},{key:"getCaret",value:function(){var t=this;this.$header.find("th").each((function(e,n){i.default(n).find(".sortable").removeClass("desc asc").addClass(i.default(n).data("field")===t.options.sortName?t.options.sortOrder:"both")}))}},{key:"updateSelected",value:function(){var t=this.$selectItem.filter(":enabled").length&&this.$selectItem.filter(":enabled").length===this.$selectItem.filter(":enabled").filter(":checked").length;this.$selectAll.add(this.$selectAll_).prop("checked",t),this.$selectItem.each((function(t,e){i.default(e).closest("tr")[i.default(e).prop("checked")?"addClass":"removeClass"]("selected")}))}},{key:"updateRows",value:function(){var t=this;this.$selectItem.each((function(e,n){t.data[i.default(n).data("index")][t.header.stateField]=i.default(n).prop("checked")}))}},{key:"resetRows",value:function(){var t,e=u(this.data);try{for(e.s();!(t=e.n()).done;){var i=t.value;this.$selectAll.prop("checked",!1),this.$selectItem.prop("checked",!1),this.header.stateField&&(i[this.header.stateField]=!1)}}catch(t){e.e(t)}finally{e.f()}this.initHiddenRows()}},{key:"trigger",value:function(e){for(var n,o,a="".concat(e,".bs.table"),s=arguments.length,r=new Array(s>1?s-1:0),l=1;l<s;l++)r[l-1]=arguments[l];(n=this.options)[t.EVENTS[a]].apply(n,[].concat(r,[this])),this.$el.trigger(i.default.Event(a,{sender:this}),r),(o=this.options).onAll.apply(o,[a].concat([].concat(r,[this]))),this.$el.trigger(i.default.Event("all.bs.table",{sender:this}),[a,r])}},{key:"resetHeader",value:function(){var t=this;clearTimeout(this.timeoutId_),this.timeoutId_=setTimeout((function(){return t.fitHeader()}),this.$el.is(":hidden")?100:0)}},{key:"fitHeader",value:function(){var t=this;if(this.$el.is(":hidden"))this.timeoutId_=setTimeout((function(){return t.fitHeader()}),100);else{var e=this.$tableBody.get(0),n=e.scrollWidth>e.clientWidth&&e.scrollHeight>e.clientHeight+this.$header.outerHeight()?ua.getScrollBarWidth():0;this.$el.css("margin-top",-this.$header.outerHeight());var o=i.default(":focus");if(o.length>0){var a=o.parents("th");if(a.length>0){var s=a.attr("data-field");if(void 0!==s){var r=this.$header.find("[data-field='".concat(s,"']"));r.length>0&&r.find(":input").addClass("focus-temp")}}}this.$header_=this.$header.clone(!0,!0),this.$selectAll_=this.$header_.find('[name="btSelectAll"]'),this.$tableHeader.css("margin-right",n).find("table").css("width",this.$el.outerWidth()).html("").attr("class",this.$el.attr("class")).append(this.$header_),this.$tableLoading.css("width",this.$el.outerWidth());var l=i.default(".focus-temp:visible:eq(0)");l.length>0&&(l.focus(),this.$header.find(".focus-temp").removeClass("focus-temp")),this.$header.find("th[data-field]").each((function(e,n){t.$header_.find(ua.sprintf('th[data-field="%s"]',i.default(n).data("field"))).data(i.default(n).data())}));for(var c=this.getVisibleFields(),h=this.$header_.find("th"),u=this.$body.find(">tr:not(.no-records-found,.virtual-scroll-top)").eq(0);u.length&&u.find('>td[colspan]:not([colspan="1"])').length;)u=u.next();var d=u.find("> *").length;u.find("> *").each((function(e,n){var o=i.default(n);if(ua.hasDetailViewIcon(t.options)&&(0===e&&"right"!==t.options.detailViewAlign||e===d-1&&"right"===t.options.detailViewAlign)){var a=h.filter(".detail"),s=a.innerWidth()-a.find(".fht-cell").width();a.find(".fht-cell").width(o.innerWidth()-s)}else{var r=e-ua.getDetailViewIndexOffset(t.options),l=t.$header_.find(ua.sprintf('th[data-field="%s"]',c[r]));l.length>1&&(l=i.default(h[o[0].cellIndex]));var u=l.innerWidth()-l.find(".fht-cell").width();l.find(".fht-cell").width(o.innerWidth()-u)}})),this.horizontalScroll(),this.trigger("post-header")}}},{key:"initFooter",value:function(){if(this.options.showFooter&&!this.options.cardView){var t=this.getData(),e=[],i="";ua.hasDetailViewIcon(this.options)&&(i='<th class="detail"><div class="th-inner"></div><div class="fht-cell"></div></th>'),i&&"right"!==this.options.detailViewAlign&&e.push(i);var n,o=u(this.columns);try{for(o.s();!(n=o.n()).done;){var a,s,l=n.value,c=[],h={},d=ua.sprintf(' class="%s"',l.class);if(!(!l.visible||this.footerData&&this.footerData.length>0&&!(l.field in this.footerData[0]))){if(this.options.cardView&&!l.cardVisible)return;if(a=ua.sprintf("text-align: %s; ",l.falign?l.falign:l.align),s=ua.sprintf("vertical-align: %s; ",l.valign),(h=ua.calculateObjectValue(null,this.options.footerStyle,[l]))&&h.css)for(var f=0,p=Object.entries(h.css);f<p.length;f++){var g=r(p[f],2),v=g[0],b=g[1];c.push("".concat(v,": ").concat(b))}h&&h.classes&&(d=ua.sprintf(' class="%s"',l.class?[l.class,h.classes].join(" "):h.classes)),e.push("<th",d,ua.sprintf(' style="%s"',a+s+c.concat().join("; ")));var m=0;this.footerData&&this.footerData.length>0&&(m=this.footerData[0]["_".concat(l.field,"_colspan")]||0),m&&e.push(' colspan="'.concat(m,'" ')),e.push(">"),e.push('<div class="th-inner">');var y="";this.footerData&&this.footerData.length>0&&(y=this.footerData[0][l.field]||""),e.push(ua.calculateObjectValue(l,l.footerFormatter,[t,y],y)),e.push("</div>"),e.push('<div class="fht-cell"></div>'),e.push("</div>"),e.push("</th>")}}}catch(t){o.e(t)}finally{o.f()}i&&"right"===this.options.detailViewAlign&&e.push(i),this.options.height||this.$tableFooter.length||(this.$el.append("<tfoot><tr></tr></tfoot>"),this.$tableFooter=this.$el.find("tfoot")),this.$tableFooter.find("tr").length||this.$tableFooter.html("<table><thead><tr></tr></thead></table>"),this.$tableFooter.find("tr").html(e.join("")),this.trigger("post-footer",this.$tableFooter)}}},{key:"fitFooter",value:function(){var t=this;if(this.$el.is(":hidden"))setTimeout((function(){return t.fitFooter()}),100);else{var e=this.$tableBody.get(0),n=e.scrollWidth>e.clientWidth&&e.scrollHeight>e.clientHeight+this.$header.outerHeight()?ua.getScrollBarWidth():0;this.$tableFooter.css("margin-right",n).find("table").css("width",this.$el.outerWidth()).attr("class",this.$el.attr("class"));var o=this.$tableFooter.find("th"),a=this.$body.find(">tr:first-child:not(.no-records-found)");for(o.find(".fht-cell").width("auto");a.length&&a.find('>td[colspan]:not([colspan="1"])').length;)a=a.next();var s=a.find("> *").length;a.find("> *").each((function(e,n){var a=i.default(n);if(ua.hasDetailViewIcon(t.options)&&(0===e&&"left"===t.options.detailViewAlign||e===s-1&&"right"===t.options.detailViewAlign)){var r=o.filter(".detail"),l=r.innerWidth()-r.find(".fht-cell").width();r.find(".fht-cell").width(a.innerWidth()-l)}else{var c=o.eq(e),h=c.innerWidth()-c.find(".fht-cell").width();c.find(".fht-cell").width(a.innerWidth()-h)}})),this.horizontalScroll()}}},{key:"horizontalScroll",value:function(){var t=this;this.$tableBody.off("scroll").on("scroll",(function(){var e=t.$tableBody.scrollLeft();t.options.showHeader&&t.options.height&&t.$tableHeader.scrollLeft(e),t.options.showFooter&&!t.options.cardView&&t.$tableFooter.scrollLeft(e),t.trigger("scroll-body",t.$tableBody)}))}},{key:"getVisibleFields",value:function(){var t,e=[],i=u(this.header.fields);try{for(i.s();!(t=i.n()).done;){var n=t.value,o=this.columns[this.fieldsColumnsIndex[n]];o&&o.visible&&e.push(n)}}catch(t){i.e(t)}finally{i.f()}return e}},{key:"initHiddenRows",value:function(){this.hiddenRows=[]}},{key:"getOptions",value:function(){var t=i.default.extend({},this.options);return delete t.data,i.default.extend(!0,{},t)}},{key:"refreshOptions",value:function(t){ua.compareObjects(this.options,t,!0)||(this.options=i.default.extend(this.options,t),this.trigger("refresh-options",this.options),this.destroy(),this.init())}},{key:"getData",value:function(t){var e=this,i=this.options.data;if(!(this.searchText||this.options.customSearch||void 0!==this.options.sortName||this.enableCustomSort)&&ua.isEmptyObject(this.filterColumns)&&ua.isEmptyObject(this.filterColumnsPartial)||t&&t.unfiltered||(i=this.data),t&&t.useCurrentPage&&(i=i.slice(this.pageFrom-1,this.pageTo)),t&&!t.includeHiddenRows){var n=this.getHiddenRows();i=i.filter((function(t){return-1===ua.findIndex(n,t)}))}return t&&t.formatted&&i.forEach((function(t){for(var i=0,n=Object.entries(t);i<n.length;i++){var o=r(n[i],2),a=o[0],s=o[1],l=e.columns[e.fieldsColumnsIndex[a]];if(!l)return;t[a]=ua.calculateObjectValue(l,e.header.formatters[l.fieldIndex],[s,t,t.index,l.field],s)}})),i}},{key:"getSelections",value:function(){var t=this;return(this.options.maintainMetaData?this.options.data:this.data).filter((function(e){return!0===e[t.header.stateField]}))}},{key:"load",value:function(t){var e,i=t;this.options.pagination&&"server"===this.options.sidePagination&&(this.options.totalRows=i[this.options.totalField],this.options.totalNotFiltered=i[this.options.totalNotFilteredField],this.footerData=i[this.options.footerField]?[i[this.options.footerField]]:void 0),e=i.fixedScroll,i=Array.isArray(i)?i:i[this.options.dataField],this.initData(i),this.initSearch(),this.initPagination(),this.initBody(e)}},{key:"append",value:function(t){this.initData(t,"append"),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)}},{key:"prepend",value:function(t){this.initData(t,"prepend"),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)}},{key:"remove",value:function(t){for(var e=0,i=this.options.data.length-1;i>=0;i--){var n=this.options.data[i];(n.hasOwnProperty(t.field)||"$index"===t.field)&&((!n.hasOwnProperty(t.field)&&"$index"===t.field&&t.values.includes(i)||t.values.includes(n[t.field]))&&(e++,this.options.data.splice(i,1)))}e&&("server"===this.options.sidePagination&&(this.options.totalRows-=e,this.data=l(this.options.data)),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0))}},{key:"removeAll",value:function(){this.options.data.length>0&&(this.options.data.splice(0,this.options.data.length),this.initSearch(),this.initPagination(),this.initBody(!0))}},{key:"insertRow",value:function(t){t.hasOwnProperty("index")&&t.hasOwnProperty("row")&&(this.options.data.splice(t.index,0,t.row),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0))}},{key:"updateRow",value:function(t){var e,n=u(Array.isArray(t)?t:[t]);try{for(n.s();!(e=n.n()).done;){var o=e.value;o.hasOwnProperty("index")&&o.hasOwnProperty("row")&&(o.hasOwnProperty("replace")&&o.replace?this.options.data[o.index]=o.row:i.default.extend(this.options.data[o.index],o.row))}}catch(t){n.e(t)}finally{n.f()}this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)}},{key:"getRowByUniqueId",value:function(t){var e,i,n,o=this.options.uniqueId,a=t,s=null;for(e=this.options.data.length-1;e>=0;e--){if((i=this.options.data[e]).hasOwnProperty(o))n=i[o];else{if(!i._data||!i._data.hasOwnProperty(o))continue;n=i._data[o]}if("string"==typeof n?a=a.toString():"number"==typeof n&&(Number(n)===n&&n%1==0?a=parseInt(a,10):n===Number(n)&&0!==n&&(a=parseFloat(a))),n===a){s=i;break}}return s}},{key:"updateByUniqueId",value:function(t){var e,n=null,o=u(Array.isArray(t)?t:[t]);try{for(o.s();!(e=o.n()).done;){var a=e.value;if(a.hasOwnProperty("id")&&a.hasOwnProperty("row")){var s=this.options.data.indexOf(this.getRowByUniqueId(a.id));-1!==s&&(a.hasOwnProperty("replace")&&a.replace?this.options.data[s]=a.row:i.default.extend(this.options.data[s],a.row),n=a.id)}}}catch(t){o.e(t)}finally{o.f()}this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0,n)}},{key:"removeByUniqueId",value:function(t){var e=this.options.data.length,i=this.getRowByUniqueId(t);i&&this.options.data.splice(this.options.data.indexOf(i),1),e!==this.options.data.length&&("server"===this.options.sidePagination&&(this.options.totalRows-=1,this.data=l(this.options.data)),this.initSearch(),this.initPagination(),this.initBody(!0))}},{key:"updateCell",value:function(t){t.hasOwnProperty("index")&&t.hasOwnProperty("field")&&t.hasOwnProperty("value")&&(this.data[t.index][t.field]=t.value,!1!==t.reinit&&(this.initSort(),this.initBody(!0)))}},{key:"updateCellByUniqueId",value:function(t){var e=this;(Array.isArray(t)?t:[t]).forEach((function(t){var i=t.id,n=t.field,o=t.value,a=e.options.data.indexOf(e.getRowByUniqueId(i));-1!==a&&(e.options.data[a][n]=o)})),!1!==t.reinit&&(this.initSort(),this.initBody(!0))}},{key:"showRow",value:function(t){this._toggleRow(t,!0)}},{key:"hideRow",value:function(t){this._toggleRow(t,!1)}},{key:"_toggleRow",value:function(t,e){var i;if(t.hasOwnProperty("index")?i=this.getData()[t.index]:t.hasOwnProperty("uniqueId")&&(i=this.getRowByUniqueId(t.uniqueId)),i){var n=ua.findIndex(this.hiddenRows,i);e||-1!==n?e&&n>-1&&this.hiddenRows.splice(n,1):this.hiddenRows.push(i),this.initBody(!0),this.initPagination()}}},{key:"getHiddenRows",value:function(t){if(t)return this.initHiddenRows(),this.initBody(!0),void this.initPagination();var e,i=[],n=u(this.getData());try{for(n.s();!(e=n.n()).done;){var o=e.value;this.hiddenRows.includes(o)&&i.push(o)}}catch(t){n.e(t)}finally{n.f()}return this.hiddenRows=i,i}},{key:"showColumn",value:function(t){var e=this;(Array.isArray(t)?t:[t]).forEach((function(t){e._toggleColumn(e.fieldsColumnsIndex[t],!0,!0)}))}},{key:"hideColumn",value:function(t){var e=this;(Array.isArray(t)?t:[t]).forEach((function(t){e._toggleColumn(e.fieldsColumnsIndex[t],!1,!0)}))}},{key:"_toggleColumn",value:function(t,e,i){if(-1!==t&&this.columns[t].visible!==e&&(this.columns[t].visible=e,this.initHeader(),this.initSearch(),this.initPagination(),this.initBody(),this.options.showColumns)){var n=this.$toolbar.find('.keep-open input:not(".toggle-all")').prop("disabled",!1);i&&n.filter(ua.sprintf('[value="%s"]',t)).prop("checked",e),n.filter(":checked").length<=this.options.minimumCountColumns&&n.filter(":checked").prop("disabled",!0)}}},{key:"getVisibleColumns",value:function(){var t=this;return this.columns.filter((function(e){return e.visible&&!t.isSelectionColumn(e)}))}},{key:"getHiddenColumns",value:function(){return this.columns.filter((function(t){return!t.visible}))}},{key:"isSelectionColumn",value:function(t){return t.radio||t.checkbox}},{key:"showAllColumns",value:function(){this._toggleAllColumns(!0)}},{key:"hideAllColumns",value:function(){this._toggleAllColumns(!1)}},{key:"_toggleAllColumns",value:function(t){var e,n=this,o=u(this.columns.slice().reverse());try{for(o.s();!(e=o.n()).done;){var a=e.value;if(a.switchable){if(!t&&this.options.showColumns&&this.getVisibleColumns().length===this.options.minimumCountColumns)continue;a.visible=t}}}catch(t){o.e(t)}finally{o.f()}if(this.initHeader(),this.initSearch(),this.initPagination(),this.initBody(),this.options.showColumns){var s=this.$toolbar.find('.keep-open input[type="checkbox"]:not(".toggle-all")').prop("disabled",!1);t?s.prop("checked",t):s.get().reverse().forEach((function(e){s.filter(":checked").length>n.options.minimumCountColumns&&i.default(e).prop("checked",t)})),s.filter(":checked").length<=this.options.minimumCountColumns&&s.filter(":checked").prop("disabled",!0)}}},{key:"mergeCells",value:function(t){var e,i,n=t.index,o=this.getVisibleFields().indexOf(t.field),a=t.rowspan||1,s=t.colspan||1,r=this.$body.find(">tr[data-index]");o+=ua.getDetailViewIndexOffset(this.options);var l=r.eq(n).find(">td").eq(o);if(!(n<0||o<0||n>=this.data.length)){for(e=n;e<n+a;e++)for(i=o;i<o+s;i++)r.eq(e).find(">td").eq(i).hide();l.attr("rowspan",a).attr("colspan",s).show()}}},{key:"checkAll",value:function(){this._toggleCheckAll(!0)}},{key:"uncheckAll",value:function(){this._toggleCheckAll(!1)}},{key:"_toggleCheckAll",value:function(t){var e=this.getSelections();this.$selectAll.add(this.$selectAll_).prop("checked",t),this.$selectItem.filter(":enabled").prop("checked",t),this.updateRows(),this.updateSelected();var i=this.getSelections();t?this.trigger("check-all",i,e):this.trigger("uncheck-all",i,e)}},{key:"checkInvert",value:function(){var t=this.$selectItem.filter(":enabled"),e=t.filter(":checked");t.each((function(t,e){i.default(e).prop("checked",!i.default(e).prop("checked"))})),this.updateRows(),this.updateSelected(),this.trigger("uncheck-some",e),e=this.getSelections(),this.trigger("check-some",e)}},{key:"check",value:function(t){this._toggleCheck(!0,t)}},{key:"uncheck",value:function(t){this._toggleCheck(!1,t)}},{key:"_toggleCheck",value:function(t,e){var i=this.$selectItem.filter('[data-index="'.concat(e,'"]')),n=this.data[e];if(i.is(":radio")||this.options.singleSelect||this.options.multipleSelectRow&&!this.multipleSelectRowCtrlKey&&!this.multipleSelectRowShiftKey){var o,a=u(this.options.data);try{for(a.s();!(o=a.n()).done;){o.value[this.header.stateField]=!1}}catch(t){a.e(t)}finally{a.f()}this.$selectItem.filter(":checked").not(i).prop("checked",!1)}if(n[this.header.stateField]=t,this.options.multipleSelectRow){if(this.multipleSelectRowShiftKey&&this.multipleSelectRowLastSelectedIndex>=0)for(var s=r(this.multipleSelectRowLastSelectedIndex<e?[this.multipleSelectRowLastSelectedIndex,e]:[e,this.multipleSelectRowLastSelectedIndex],2),l=s[0],c=s[1],h=l+1;h<c;h++)this.data[h][this.header.stateField]=!0,this.$selectItem.filter('[data-index="'.concat(h,'"]')).prop("checked",!0);this.multipleSelectRowCtrlKey=!1,this.multipleSelectRowShiftKey=!1,this.multipleSelectRowLastSelectedIndex=t?e:-1}i.prop("checked",t),this.updateSelected(),this.trigger(t?"check":"uncheck",this.data[e],i)}},{key:"checkBy",value:function(t){this._toggleCheckBy(!0,t)}},{key:"uncheckBy",value:function(t){this._toggleCheckBy(!1,t)}},{key:"_toggleCheckBy",value:function(t,e){var i=this;if(e.hasOwnProperty("field")&&e.hasOwnProperty("values")){var n=[];this.data.forEach((function(o,a){if(!o.hasOwnProperty(e.field))return!1;if(e.values.includes(o[e.field])){var s=i.$selectItem.filter(":enabled").filter(ua.sprintf('[data-index="%s"]',a)),r=!!e.hasOwnProperty("onlyCurrentPage")&&e.onlyCurrentPage;if(!(s=t?s.not(":checked"):s.filter(":checked")).length&&r)return;s.prop("checked",t),o[i.header.stateField]=t,n.push(o),i.trigger(t?"check":"uncheck",o,s)}})),this.updateSelected(),this.trigger(t?"check-some":"uncheck-some",n)}}},{key:"refresh",value:function(t){t&&t.url&&(this.options.url=t.url),t&&t.pageNumber&&(this.options.pageNumber=t.pageNumber),t&&t.pageSize&&(this.options.pageSize=t.pageSize),this.trigger("refresh",this.initServer(t&&t.silent,t&&t.query,t&&t.url))}},{key:"destroy",value:function(){this.$el.insertBefore(this.$container),i.default(this.options.toolbar).insertBefore(this.$el),this.$container.next().remove(),this.$container.remove(),this.$el.html(this.$el_.html()).css("margin-top","0").attr("class",this.$el_.attr("class")||"")}},{key:"resetView",value:function(t){var e=0;if(t&&t.height&&(this.options.height=t.height),this.$tableContainer.toggleClass("has-card-view",this.options.cardView),!this.options.cardView&&this.options.showHeader&&this.options.height?(this.$tableHeader.show(),this.resetHeader(),e+=this.$header.outerHeight(!0)+1):(this.$tableHeader.hide(),this.trigger("post-header")),!this.options.cardView&&this.options.showFooter&&(this.$tableFooter.show(),this.fitFooter(),this.options.height&&(e+=this.$tableFooter.outerHeight(!0))),this.$container.hasClass("fullscreen"))this.$tableContainer.css("height",""),this.$tableContainer.css("width","");else if(this.options.height){this.$tableBorder&&(this.$tableBorder.css("width",""),this.$tableBorder.css("height",""));var i=this.$toolbar.outerHeight(!0),n=this.$pagination.outerHeight(!0),o=this.options.height-i-n,a=this.$tableBody.find(">table"),s=a.outerHeight();if(this.$tableContainer.css("height","".concat(o,"px")),this.$tableBorder&&a.is(":visible")){var r=o-s-2;this.$tableBody[0].scrollWidth-this.$tableBody.innerWidth()&&(r-=ua.getScrollBarWidth()),this.$tableBorder.css("width","".concat(a.outerWidth(),"px")),this.$tableBorder.css("height","".concat(r,"px"))}}this.options.cardView?(this.$el.css("margin-top","0"),this.$tableContainer.css("padding-bottom","0"),this.$tableFooter.hide()):(this.getCaret(),this.$tableContainer.css("padding-bottom","".concat(e,"px"))),this.trigger("reset-view")}},{key:"showLoading",value:function(){this.$tableLoading.toggleClass("open",!0);var t=this.options.loadingFontSize;"auto"===this.options.loadingFontSize&&(t=.04*this.$tableLoading.width(),t=Math.max(12,t),t=Math.min(32,t),t="".concat(t,"px")),this.$tableLoading.find(".loading-text").css("font-size",t)}},{key:"hideLoading",value:function(){this.$tableLoading.toggleClass("open",!1)}},{key:"togglePagination",value:function(){this.options.pagination=!this.options.pagination;var t=this.options.showButtonIcons?this.options.pagination?this.options.icons.paginationSwitchDown:this.options.icons.paginationSwitchUp:"",e=this.options.showButtonText?this.options.pagination?this.options.formatPaginationSwitchUp():this.options.formatPaginationSwitchDown():"";this.$toolbar.find('button[name="paginationSwitch"]').html("".concat(ua.sprintf(this.constants.html.icon,this.options.iconsPrefix,t)," ").concat(e)),this.updatePagination(),this.trigger("toggle-pagination",this.options.pagination)}},{key:"toggleFullscreen",value:function(){this.$el.closest(".bootstrap-table").toggleClass("fullscreen"),this.resetView()}},{key:"toggleView",value:function(){this.options.cardView=!this.options.cardView,this.initHeader();var t=this.options.showButtonIcons?this.options.cardView?this.options.icons.toggleOn:this.options.icons.toggleOff:"",e=this.options.showButtonText?this.options.cardView?this.options.formatToggleOff():this.options.formatToggleOn():"";this.$toolbar.find('button[name="toggle"]').html("".concat(ua.sprintf(this.constants.html.icon,this.options.iconsPrefix,t)," ").concat(e)),this.initBody(),this.trigger("toggle",this.options.cardView)}},{key:"resetSearch",value:function(t){var e=ua.getSearchInput(this);e.val(t||""),this.onSearch({currentTarget:e})}},{key:"filterBy",value:function(t,e){this.filterOptions=ua.isEmptyObject(e)?this.options.filterOptions:i.default.extend(this.options.filterOptions,e),this.filterColumns=ua.isEmptyObject(t)?{}:t,this.options.pageNumber=1,this.initSearch(),this.updatePagination()}},{key:"scrollTo",value:function(t){var e={unit:"px",value:0};"object"===n(t)?e=Object.assign(e,t):"string"==typeof t&&"bottom"===t?e.value=this.$tableBody[0].scrollHeight:"string"!=typeof t&&"number"!=typeof t||(e.value=t);var o=e.value;"rows"===e.unit&&(o=0,this.$body.find("> tr:lt(".concat(e.value,")")).each((function(t,e){o+=i.default(e).outerHeight(!0)}))),this.$tableBody.scrollTop(o)}},{key:"getScrollPosition",value:function(){return this.$tableBody.scrollTop()}},{key:"selectPage",value:function(t){t>0&&t<=this.options.totalPages&&(this.options.pageNumber=t,this.updatePagination())}},{key:"prevPage",value:function(){this.options.pageNumber>1&&(this.options.pageNumber--,this.updatePagination())}},{key:"nextPage",value:function(){this.options.pageNumber<this.options.totalPages&&(this.options.pageNumber++,this.updatePagination())}},{key:"toggleDetailView",value:function(t,e){this.$body.find(ua.sprintf('> tr[data-index="%s"]',t)).next().is("tr.detail-view")?this.collapseRow(t):this.expandRow(t,e),this.resetView()}},{key:"expandRow",value:function(t,e){var i=this.data[t],n=this.$body.find(ua.sprintf('> tr[data-index="%s"][data-has-detail-view]',t));if(this.options.detailViewIcon&&n.find("a.detail-icon").html(ua.sprintf(this.constants.html.icon,this.options.iconsPrefix,this.options.icons.detailClose)),!n.next().is("tr.detail-view")){n.after(ua.sprintf('<tr class="detail-view"><td colspan="%s"></td></tr>',n.children("td").length));var o=n.next().find("td"),a=e||this.options.detailFormatter,s=ua.calculateObjectValue(this.options,a,[t,i,o],"");1===o.length&&o.append(s),this.trigger("expand-row",t,i,o)}}},{key:"expandRowByUniqueId",value:function(t){var e=this.getRowByUniqueId(t);e&&this.expandRow(this.data.indexOf(e))}},{key:"collapseRow",value:function(t){var e=this.data[t],i=this.$body.find(ua.sprintf('> tr[data-index="%s"][data-has-detail-view]',t));i.next().is("tr.detail-view")&&(this.options.detailViewIcon&&i.find("a.detail-icon").html(ua.sprintf(this.constants.html.icon,this.options.iconsPrefix,this.options.icons.detailOpen)),this.trigger("collapse-row",t,e,i.next()),i.next().remove())}},{key:"collapseRowByUniqueId",value:function(t){var e=this.getRowByUniqueId(t);e&&this.collapseRow(this.data.indexOf(e))}},{key:"expandAllRows",value:function(){for(var t=this.$body.find("> tr[data-index][data-has-detail-view]"),e=0;e<t.length;e++)this.expandRow(i.default(t[e]).data("index"))}},{key:"collapseAllRows",value:function(){for(var t=this.$body.find("> tr[data-index][data-has-detail-view]"),e=0;e<t.length;e++)this.collapseRow(i.default(t[e]).data("index"))}},{key:"updateColumnTitle",value:function(t){t.hasOwnProperty("field")&&t.hasOwnProperty("title")&&(this.columns[this.fieldsColumnsIndex[t.field]].title=this.options.escape?ua.escapeHTML(t.title):t.title,this.columns[this.fieldsColumnsIndex[t.field]].visible&&(this.$header.find("th[data-field]").each((function(e,n){if(i.default(n).data("field")===t.field)return i.default(i.default(n).find(".th-inner")[0]).text(t.title),!1})),this.resetView()))}},{key:"updateFormatText",value:function(t,e){/^format/.test(t)&&this.options[t]&&("string"==typeof e?this.options[t]=function(){return e}:"function"==typeof e&&(this.options[t]=e),this.initToolbar(),this.initPagination(),this.initBody())}}]),t}();return fa.VERSION=Qo.VERSION,fa.DEFAULTS=Qo.DEFAULTS,fa.LOCALES=Qo.LOCALES,fa.COLUMN_DEFAULTS=Qo.COLUMN_DEFAULTS,fa.METHODS=Qo.METHODS,fa.EVENTS=Qo.EVENTS,i.default.BootstrapTable=fa,i.default.fn.bootstrapTable=function(t){for(var e=arguments.length,o=new Array(e>1?e-1:0),a=1;a<e;a++)o[a-1]=arguments[a];var s;return this.each((function(e,a){var r=i.default(a).data("bootstrap.table"),l=i.default.extend({},fa.DEFAULTS,i.default(a).data(),"object"===n(t)&&t);if("string"==typeof t){var c;if(!Qo.METHODS.includes(t))throw new Error("Unknown method: ".concat(t));if(!r)return;s=(c=r)[t].apply(c,o),"destroy"===t&&i.default(a).removeData("bootstrap.table")}r||(r=new i.default.BootstrapTable(a,l),i.default(a).data("bootstrap.table",r),r.init())})),void 0===s?this:s},i.default.fn.bootstrapTable.Constructor=fa,i.default.fn.bootstrapTable.theme=Qo.THEME,i.default.fn.bootstrapTable.VERSION=Qo.VERSION,i.default.fn.bootstrapTable.defaults=fa.DEFAULTS,i.default.fn.bootstrapTable.columnDefaults=fa.COLUMN_DEFAULTS,i.default.fn.bootstrapTable.events=fa.EVENTS,i.default.fn.bootstrapTable.locales=fa.LOCALES,i.default.fn.bootstrapTable.methods=fa.METHODS,i.default.fn.bootstrapTable.utils=ua,i.default((function(){i.default('[data-toggle="table"]').bootstrapTable()})),fa}));
